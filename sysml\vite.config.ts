import { defineConfig } from 'vite';
import * as path from 'path';
import importMetaUrlPlugin from '@codingame/esbuild-import-meta-url-plugin';
import ViteMonacoPlugin from 'vite-plugin-monaco-editor'

import vue from '@vitejs/plugin-vue';

export default defineConfig( () => {
    const config = {
        build: {
            target: 'esnext',
            rollupOptions: {
                input: {
                    index: path.resolve(__dirname, 'index.html'),
                    monacoClassic: path.resolve(__dirname, 'static/monacoClassic.html'),
                    monacoExtended: path.resolve(__dirname, 'static/monacoExtended.html'),
                }
            }
        },
        resolve: {
            dedupe: ['vscode']
        },
        optimizeDeps: {
            esbuildOptions: {
                plugins: [
                    importMetaUrlPlugin
                ]
            }
        },
        plugins: [vue()],
        server: {
            proxy: {
                "/api": {
                    //要访问的跨域的域名
                    target: "http://127.0.0.1:8000/",
                    ws: true,
                    secure: false, // 使用的是http协议则设置为false，https协议则设置为true
                    changeOrigin: true,
                    // pathRewrite: {
                    //   '^/api': ''
                    // }
                },
            },
        },
    };
    return config;

});
