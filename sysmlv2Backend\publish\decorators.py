from functools import wraps

from django.http import JsonResponse
from jwt import exceptions

from .user_token import parse_token


def check_request_method(method):
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            if request.method != method:
                return JsonResponse({'code': 1001, 'msg': "请求方式错误"})
            return func(request, *args, **kwargs)

        return wrapper

    return decorator


def login_required(func):
    @wraps(func)
    def wrapper(request, *args, **kwargs):
        auth = request.META.get('HTTP_AUTHORIZATION')
        if not auth:
            return JsonResponse({'code': 2004, 'msg': "用户未登录"})
        try:
            token = auth.split(' ')[1]
            parse_token(token)
        except exceptions.ExpiredSignatureError:
            return JsonResponse({'code': 2005, 'msg': "登录过期"})
        except Exception as e:
            print(e)
            return JsonResponse({'code': 1000, 'msg': "token 错误"})
        return func(request, *args, **kwargs)

    return wrapper
