<template>
  <div class="common-layout">
    <el-container class="total-container">
      <el-aside width="220px" class="side-bar">
        <el-row class="doc-card-head">
          <el-col :span="20">
            <span class="doc-card-title">
              项目根目录
            </span>
          </el-col>
          <el-col
              :span="2"
              class="doc-card-icon"
              @click="newDocVisible = true"
          >
            <div style="max-width: 38px">
              <el-icon style="max-width: 38px"><DocumentAdd /></el-icon>
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="新建图"
                  placement="top-start"
              >

              </el-tooltip>
            </div>

          </el-col>
          <el-col
              :span="2"
              class="doc-card-icon"
              @click="newFolderVisible = true"
          >
            <el-tooltip
                class="box-item"
                effect="dark"
                content="新建命名空间夹"
                placement="top-start"
            >
              <el-icon style="max-width: 38px"><FolderAdd /></el-icon>
            </el-tooltip>
          </el-col>
        </el-row>

        <folder-card
            v-for="folder in content.folderList"
            :key="folder.folder_id"
            :folder-obj="folder"
            :re-get-docs="getElements"
            :open-doc="openDoc"
            :selected-doc="currentDoc"
        >
        </folder-card>

        <doc-card
            v-for="doc in content.docList"
            :key="doc.id"
            :doc-obj="doc"
            :re-get-docs="getElements"
            :open-doc="openDoc"
            :is-selected="doc.id === currentDoc.id"
        >
        </doc-card>
      </el-aside>

      <el-container>
        <el-header class="header-container">
          <el-row>

            <el-col :span="4">
              <div class="header-element-container">
                <img src="../../assets/img/logo.png" class="logo" alt="logo">
              </div>
            </el-col>
            <el-col :span="16">

            </el-col>
            <el-col :span="4">
              <div class="header-element-container">
                <el-button v-if="baseInfo.isLogin" @click="logout" round>Logout</el-button>
                <el-button v-else @click="toLogin_User" round>Login</el-button>
              </div>

            </el-col>
          </el-row>
        </el-header>
        
        <el-main class="main-container">


          <el-row>
            <el-col :span="12">
              <el-row>

                <el-col :span="7">
                  <el-button plain>导入模板</el-button>
                </el-col>
                <el-col :span="9">

                </el-col>
                <el-col :span="8">
                  <el-button plain @click="saveDoc">保存</el-button>
                  <el-button plain @click="outputCode">生成图表</el-button>
                </el-col>
              </el-row>
              <monaco-editor
                  ref="editor"
                  :code-content="currentDoc.content"
                  :key="componentKey"
              ></monaco-editor>
            </el-col>
            <el-col :span="12">
              <sysml-diagram ref="diagramRef"></sysml-diagram>
            </el-col>
          </el-row>

        </el-main>
      </el-container>
    </el-container>
  </div>

  <el-dialog v-model="newDocVisible" title="新建图" style="max-width: 400px">
    <el-form :model="newDocForm">
      <el-form-item label="图名称" :label-width="formLabelWidth">
        <el-input v-model="newDocForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="newDocVisible = false">取消</el-button>
        <el-button type="primary" @click="createDoc(newDocForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog
      v-model="newFolderVisible"
      title="新建命名空间"
      style="max-width: 400px"
  >
    <el-form :model="newFolderForm">
      <el-form-item label="名称" :label-width="formLabelWidth">
        <el-input v-model="newFolderForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="newFolderVisible = false">取消</el-button>
        <el-button type="primary" @click="createFolder(newFolderForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import DocCard from "../../components/DocCard.vue";
import FolderCard from "../../components/FolderCard.vue";
import { ref, onBeforeMount, onMounted, nextTick } from "vue";
import axios from "axios";
import { ElMessage } from "element-plus";
import {useRouter} from "vue-router";
import MonacoEditor from "../../components/MonacoEditor.vue";
import SysmlDiagram from "../../components/SysmlDiagram.vue";
import { FileAddOutlined } from '@ant-design/icons-vue';

const newDocForm = ref({
  title: "",
});
const newFolderForm = ref({
  title: "",
});
const newDocVisible = ref(false);
const newFolderVisible = ref(false);
const formLabelWidth = "90px";

const content = ref({
  folderList: [],
  docList: [],
  project_root_dir: 0
});
const baseInfo = ref({
  isLogin: false,
  currentUser: {},
  project_id: ''
})
const currentDoc = ref({
  id: -1,
  isSelected: true,
  content: "//请点击打开任意图来启动SysMLv2代码编辑器",
})

const componentKey = ref(0);

const forceRerender = () => {
  componentKey.value++; // 修改 key 触发组件重置
};

const editor = ref(null);
const diagramRef = ref(null);

const router = useRouter();

const initUser = () => {
  let user = JSON.parse(window.localStorage.getItem("user"));
  if (user === null) {
    baseInfo.value.isLogin = false;
  } else {
    baseInfo.value.isLogin = true;
    baseInfo.value.currentUser = user;
    baseInfo.value.project_id = router.currentRoute.value.params.projectID;
  }
  // baseInfo.value.root_dir = baseInfo.value.currentUser.root_dir_id;
}

const getElements = () => {
  // 获取根目录下的文件和文件夹
  if (baseInfo.value.isLogin === false) {
    return;
  }

  console.log("项目id为" + baseInfo.value.project_id)

  let data = JSON.stringify({
    // base_dir_id: baseInfo.value.root_dir
    base_project_id: baseInfo.value.project_id
  });

  let config = {
    method: 'post',
    url: '/api/getElements',
    headers: {
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        content.value.project_root_dir = response.data.data.root_dir_id
        content.value.folderList = response.data.data.folder_list;
        content.value.docList = response.data.data.doc_list;
      })
      .catch(function (error) {
        console.log(error);
      });
}

onMounted(() => {
  // testInit();
  getElements();
});

onBeforeMount(() => {
  initUser();
})

const createDoc = (docTitle) => {
  //在根目录下新建文档
  if (docTitle.length === 0) {
    ElMessage({
      showClose: true,
      message: "文档名称不可为空",
      type: "error",
    });
    return;
  }

  newDocVisible.value = !newDocVisible.value;

  let data = JSON.stringify({
    title: docTitle,
    base_dir_id: content.value.project_root_dir
  });

  let config = {
    method: 'post',
    url: '/api/createDoc',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "创建成功",
            type: "success",
          });
        }

        getElements();
      })
      .catch(function (error) {
        console.log(error);
      });
}

const createFolder = (folderTitle) => {
  //新建文件夹
  if (folderTitle.length === 0) {
    ElMessage({
      showClose: true,
      message: "文件夹名称不可为空",
      type: "error",
    });
    return;
  }

  newFolderVisible.value = !newFolderVisible.value;

  const data = JSON.stringify({
    title: folderTitle,
    base_dir_id: content.value.project_root_dir
  });

  var config = {
    method: 'post',
    url: '/api/createFolder',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "创建成功",
            type: "success",
          });
        }

        getElements();
      })
      .catch(function (error) {
        console.log(error);
      });
}

const saveDoc = () => {
  console.log('保存文档' + editor.value.getCodeContent());

  let data = JSON.stringify({
    target_doc_id: currentDoc.value.id,
    content: editor.value.getCodeContent()
  });

  let config = {
    method: 'post',
    url: '/api/saveDoc',
    headers: {
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "保存成功",
            type: "success",
          });
        }
      })
      .catch(function (error) {
        console.log(error);
      });
}

const openDoc = (targetId) => {
  console.log('打开文档: ' + targetId);
  currentDoc.value.id = targetId;

  let data = JSON.stringify({
    // base_dir_id: baseInfo.value.root_dir
    target_doc_id: targetId
  });

  let config = {
    method: 'post',
    url: '/api/getDocContent',
    headers: {
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));
        currentDoc.value.content = response.data.data;
        forceRerender();

      })
      .catch(function (error) {
        console.log(error);
      });


}

const outputCode = () => {
  console.log('outputCode() ' + editor.value.getCodeContent());
  // if (window.localStorage) {
  //   const storedCode = window.localStorage.getItem('mainCode');
  //   if (storedCode !== null) {
  //     console.log(storedCode);
  //   } else {
  //     console.log("code null");
  //   }
  // }
  diagramRef.value.outputDiagram(editor.value.getCodeContent())
}

const toLogin_User = () => {
  router.push({ path: "/login" });
};

const logout = () => {
  localStorage.clear();
  window.location.reload();
}
</script>

<style scoped>
.common-layout {
  position: absolute;
  height: 100%;
  width: calc(100vw - 120px);
}

.total-container {
  height: 100%;
  width: 100%;
}

.side-bar {
  margin-left: 20px;
  /*height: 100%;*/

  padding-top: 5px;
  padding-right: 15px;
  border-radius: 10px;
  box-shadow: 0 0 1px rgba(64, 62, 62, 0.91);
}

.doc-card-head {
  line-height: 36px;
}

.doc-card-title {
  margin-left: 15px;
  font-size: 20px;
}

.doc-card-icon {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
.doc-card-icon:hover {
  background-color: #d1d1d7;
}

.header-container {
  height: 50px;
}

.header-element-container {
  height: 50px;
  border: none;
  display:flex;
  //justify-content: center;
  align-items:center;
}

.logo {
  height: 50px;
}
</style>