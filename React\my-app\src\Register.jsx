import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import './Register.css';

const Register = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: '',
    username: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const validateForm = () => {
    const newErrors = {};
    
    // Email validation
    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
    if (!formData.email) {
      newErrors.email = '请输入邮箱';
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }
    
    // Username validation
    if (!formData.username) {
      newErrors.username = '请输入用户名';
    } else if (formData.username.length < 3) {
      newErrors.username = '用户名至少需要3个字符';
    }
    
    // Password validation
    if (!formData.password) {
      newErrors.password = '请输入密码';
    } else if (formData.password.length < 8) {
      newErrors.password = '密码至少需要8个字符';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      try {
        // 这里是注册逻辑，实际项目中应该调用API
        console.log('注册信息:', formData);
        
        // 模拟注册成功
        setShowSuccessModal(true);
        
        // 2秒后跳转到登录页
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      } catch (error) {
        console.error('注册失败:', error);
        setErrors({ submit: '注册失败，请稍后再试' });
      }
    }
  };

  const navigateToLogin = () => {
    navigate('/login');
  };

  return (
    <div className='register-container'>
      <div className='register-left'>
        <div className='register-image'>
          {/* 这里可以放置产品logo或宣传图片 */}
          <h1>SysML v2 Web 建模平台</h1>
          <p>强大的系统建模工具，助您快速构建精确模型</p>
        </div>
      </div>
      
      <div className='register-right'>
        <div className='register-form-container'>
          <h2>创建账号</h2>
          <form className='register-form' onSubmit={handleSubmit}>
            <div className='form-group'>
              <label htmlFor='email'>邮箱</label>
              <input
                type='email'
                id='email'
                name='email'
                placeholder='<EMAIL>'
                value={formData.email}
                onChange={handleChange}
                className={errors.email ? 'input-error' : ''}
              />
              {errors.email && <p className='error-message'>{errors.email}</p>}
            </div>
            
            <div className='form-group'>
              <label htmlFor='username'>用户名</label>
              <input
                type='text'
                id='username'
                name='username'
                placeholder='用户名'
                value={formData.username}
                onChange={handleChange}
                className={errors.username ? 'input-error' : ''}
              />
              {errors.username && <p className='error-message'>{errors.username}</p>}
            </div>
            
            <div className='form-group'>
              <label htmlFor='password'>密码</label>
              <input
                type='password'
                id='password'
                name='password'
                placeholder='至少 8 位'
                value={formData.password}
                onChange={handleChange}
                className={errors.password ? 'input-error' : ''}
              />
              {errors.password && <p className='error-message'>{errors.password}</p>}
            </div>
            
            {errors.submit && <p className='error-message submit-error'>{errors.submit}</p>}
            
            <button type='submit' className='register-button'>注册</button>
          </form>
          
          <p className='login-link'>
            已有账号？<span onClick={navigateToLogin}>去登录</span>
          </p>
        </div>
      </div>
      
      {/* 注册成功弹窗 */}
      {showSuccessModal && (
        <div className='success-modal'>
          <div className='modal-content'>
            <h3>注册成功！</h3>
            <p>即将跳转到登录页面...</p>
          </div>
        </div>
      )}
    </div>
  );
};


// 直接渲染这个组件
// ReactDOM.render(
//   <Register />,
//   document.getElementById('root')
// );

export default Register;