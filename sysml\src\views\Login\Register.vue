<template>
  <div id="poster">
    <el-form
        :model="registerForm"
        ref="registerFormRef"
        status-icon
        :rules="rules"
        class="register_container"
        label-position="left"
        label-width="80px"
    >
      <h2 class="register_title">新用户注册</h2>

      <el-form-item label="用户名" prop="user_ID">
        <el-input
            type="text"
            v-model="registerForm.user_name"
            autocomplete="off"
            placeholder="请输入用户名"
            :prefix-icon="UserFilled"
        ></el-input>
      </el-form-item>

      <el-form-item label="密码" prop="pwd">
        <el-input
            type="password"
            v-model="registerForm.pwd"
            autocomplete="off"
            placeholder="请输入密码"
            :prefix-icon="Key"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="checkPwd" style="width: 100%">
        <el-input
            type="password"
            v-model="registerForm.checkPwd"
            autocomplete="off"
            placeholder="请再次输入密码"
            :prefix-icon="Key"
        ></el-input>
      </el-form-item>

      <div class="register_buttons">
        <el-button @click="toLogin_User">去登录</el-button>
        <el-button type="primary" @click="submitForm">注册</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup>
import axios from "axios";
import {
  UserFilled,
  Key,
} from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { ref } from "vue";
import { ElMessage } from "element-plus";

const registerForm = ref({
  user_name: "",
  pwd: "",
  checkPwd: "",
});

let validateUser_ID = (rule, value, callback) => {
  if (registerForm.value.user_name === "") {
    callback(new Error("请输入用户名"));
  } else {
    let len = registerForm.value.user_name.length;
    if (!(len >= 2 && len <= 16)) {
      callback(new Error("用户名长度应为2-16"));
    }
    callback();
  }
};

let validatePwd = (rule, value, callback) => {
  if (registerForm.value.pwd === "") {
    callback(new Error("请输入密码"));
  } else {
    let len = registerForm.value.pwd.length;
    if (!(len >= 6 && len <= 16)) {
      callback(new Error("密码长度应为6-16"));
    }
    // if (this.ruleForm.checkPwd.length !== '') {
    //   this.$refs.ruleForm.validateField('checkPwd');
    // }
    callback();
  }
};
let validatePwd2 = (rule, value, callback) => {
  if (registerForm.value.checkPwd === "") {
    callback(new Error("请再次输入密码"));
  } else if (registerForm.value.checkPwd !== registerForm.value.pwd) {
    callback(new Error("两次密码不一致"));
  } else {
    callback();
  }
};

let rules = {
  user_name: [{ validator: validateUser_ID, trigger: "blur" }],
  pwd: [{ validator: validatePwd, trigger: "blur" }],
  checkPwd: [{ validator: validatePwd2, trigger: "blur" }],
};

const router = useRouter();

axios.defaults.baseURL = "/api";

const registerFormRef = ref();

let submitForm = () => {
  registerFormRef.value.validate((valid) => {
    if (valid) {
      let data = JSON.stringify({
        username: registerForm.value.user_name,
        password: registerForm.value.pwd,
      });

      // console.log(data);

      let config = {
        method: "post",
        url: 'register',
        headers: {
          'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
          'Content-Type': 'application/json'
        },
        data: data,
      };

      axios(config)
          .then(function (response) {
            console.log(JSON.stringify(response.data));

            ElMessage({
              showClose: true,
              message: "注册成功！",
              type: "success",
            });
            router.push("login");
          })
          .catch(function (error) {
            console.log(error);
          });
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

let toLogin_User = () => {
  router.push({ path: "/login" });
};
</script>

<style scoped>
#poster {
  background-position: center;
  background-size: cover;
  background-image: url("../../assets/img/Login/background.svg");
  height: 100%;
  width: 100%;
  position: fixed;
  margin: 0px;
  padding: 0px;
}
.register_container {
  border-radius: 15px;
  background-clip: padding-box;
  margin: 90px auto;
  width: 600px;
  padding: 35px 35px 15px 35px;
  background: #fff;
  border: 1px solid #eaeaea;
  box-shadow: 0 0 25px #cac6c6;
}
.register_title {
  margin-left: auto;
  text-align: center;
  /*margin-right: auto;*/
  margin-bottom: 20px;
  color: #505458;
}
.register_buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  /*margin: 0px;*/
}

.form-item {
  width: 100%;
}
</style>
