# Generated by Django 4.2.20 on 2025-05-11 21:37

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Doc",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "doc_title",
                    models.CharField(
                        default="default doc title",
                        max_length=100,
                        verbose_name="doc_title",
                    ),
                ),
                (
                    "doc_content",
                    models.TextField(blank=True, null=True, verbose_name="doc_content"),
                ),
                (
                    "base_dir_id",
                    models.IntegerField(default=-1, verbose_name="base_dir_id"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Folder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "folder_title",
                    models.Char<PERSON>ield(
                        default="default folder title",
                        max_length=100,
                        verbose_name="folder_title",
                    ),
                ),
                (
                    "base_dir_id",
                    models.IntegerField(default=-1, verbose_name="base_dir_id"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "project_title",
                    models.CharField(
                        default="default doc title",
                        max_length=100,
                        verbose_name="doc_title",
                    ),
                ),
                (
                    "base_user_id",
                    models.IntegerField(default=-3, verbose_name="base_dir_id"),
                ),
            ],
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "username",
                    models.CharField(
                        default="default name", max_length=100, verbose_name="username"
                    ),
                ),
                ("password", models.CharField(max_length=100, verbose_name="password")),
            ],
        ),
    ]
