<template>
  <div class="flex flex-col items-center w-full">
    <div class="mb-6 p-4 bg-gray-100 rounded-lg w-full">
      <div class="mb-4">

        <select
            v-model="selectedDiagramType"
            class="w-full p-2 border border-gray-300 rounded-lg"
            @change="parseAndRender">
          <option value="none">请选择图表类型</option>
          <option value="stateDiagram">状态图</option>
          <option value="structureDiagram">结构图</option>
          <option value="actionDiagram">活动图</option>
          <option value="requirementDiagram">需求图</option>
          <option value="useCaseDiagram">用例图</option>
          <option value="definitionDiagram">定义图</option>
        </select>
<!--        <button-->
<!--            @click="parseAndRender"-->
<!--            class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">-->
<!--          生成图表-->
<!--        </button>-->
      </div>
    </div>

    <!-- SVG容器 -->
    <div class="w-full overflow-auto border-2 border-gray-200 rounded-lg p-4 bg-white min-h-64">

      <div class="diagram-container relative"
           :style="{ transform: `scale(${scale})`, transformOrigin: 'top left' }"
           @wheel.prevent="handleWheel">
        <component :is="currentDiagram" :parsedData="parsedData" :fitToView="fitToView" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, h } from 'vue';

// 定义各种图表元素的类型接口
// 状态图接口
interface StateDefinition {
  type: string;
  name: string;
  stereotype?: string;
  typeName?: string;
  actions?: {
    entry?: string;
    do?: string;
    exit?: string;
  };
  substates?: StateDefinition[];
  transitions?: Transition[];
  isParallel?: boolean;
}

interface Transition {
  source: string;
  target: string;
  trigger?: string;
  guard?: string;
  effect?: string;
}

// 结构图接口
interface StructureElement {
  type: string;
  name: string;
  stereotype?: string;
  typeName?: string;
  parts?: StructureElement[];
  ports?: PortElement[];
  connections?: ConnectionElement[];
  attributes?: AttributeElement[];
}

interface PortElement {
  name: string;
  typeName?: string;
  isConjugated?: boolean;
  nestedPorts?: PortElement[];
}

interface ConnectionElement {
  source: string;
  target: string;
  name?: string;
  typeName?: string;
}

interface AttributeElement {
  name: string;
  typeName: string;
  value?: string;
  multiplicity?: string;
}

// 活动图接口
interface ActionElement {
  type: string;
  name: string;
  typeName?: string;
  parameters?: ParameterElement[];
  subactions?: ActionElement[];
  flows?: FlowElement[];
  successions?: SuccessionElement[];
  isControl?: boolean;
  controlType?: string;
}

interface ParameterElement {
  name: string;
  typeName: string;
  direction: string;
  multiplicity?: string;
}

interface FlowElement {
  source: string;
  target: string;
  payload?: string;
}

interface SuccessionElement {
  source: string;
  target: string;
  guard?: string;
}

// 需求图接口
interface RequirementElement {
  type: string;
  name: string;
  id?: string;
  text?: string;
  subrequirements?: RequirementElement[];
  satisfiedBy?: string[];
  verifiedBy?: string[];
}

// 用例图接口
interface UseCaseElement {
  type: string;
  name: string;
  subject?: string;
  actors?: ActorElement[];
  includes?: string[];
  extends?: string[];
}

interface ActorElement {
  name: string;
  typeName?: string;
}

// 定义图接口
interface DefinitionElement {
  type: string;
  name: string;
  specializes?: string[];
  features?: FeatureElement[];
}

interface FeatureElement {
  type: string;
  name: string;
  typeName?: string;
  multiplicity?: string;
}

// 通用解析后的数据结构
interface ParsedData {
  type: string;
  elements: any[]; // 将在具体解析中转换为对应类型
}

export default defineComponent({
  name: 'SysmlDiagram',

  setup(props, { expose }) {
    // 用户输入的SysMLv2代码
    const sysmlCode = ref<string>('');

    // 选择的图表类型
    const selectedDiagramType = ref<string>('none');

    // 解析后的数据
    const parsedData = ref<ParsedData | null>(null);

    // 缩放控制
    const scale = ref<number>(1.0);
    const fitToView = ref<boolean>(true);

    const outputDiagram = (codeContent) => {
      sysmlCode.value = codeContent;
      parseAndRender();
    }

    // 缩放控制函数
    const zoomIn = () => {
      scale.value = Math.min(2.0, scale.value + 0.1);
    };

    const zoomOut = () => {
      scale.value = Math.max(0.2, scale.value - 0.1);
    };

    const resetView = () => {
      scale.value = 1.0;
    };

    const handleWheel = (event: WheelEvent) => {
      if (event.ctrlKey || event.metaKey) {
        if (event.deltaY < 0) {
          zoomIn();
        } else {
          zoomOut();
        }
      }
    };

    // 解析SysMLv2代码并渲染图形
    const parseAndRender = () => {
      const code = sysmlCode.value.trim();

      if (!code || selectedDiagramType.value === 'none') {
        parsedData.value = null;
        return;
      }

      // 根据所选图表类型进行解析
      switch (selectedDiagramType.value) {
        case 'stateDiagram':
          parseStateDefinition(code);
          break;
        case 'structureDiagram':
          parseStructureDefinition(code);
          break;
        case 'actionDiagram':
          parseActionDefinition(code);
          break;
        case 'requirementDiagram':
          parseRequirementDefinition(code);
          break;
        case 'useCaseDiagram':
          parseUseCaseDefinition(code);
          break;
        case 'definitionDiagram':
          parseDefinitionDefinition(code);
          break;
        default:
          parsedData.value = null;
      }
    };

    // 解析状态定义
    const parseStateDefinition = (code: string) => {
      // 设置初始数据结构
      const parsed: ParsedData = {
        type: 'stateDiagram',
        elements: []
      };

      // 提取状态名称
      const stateDefMatch = code.match(/state\s+(?:def\s+)?(\w+)(?:\s*:\s*(\w+))?/);
      if (stateDefMatch) {
        const state: StateDefinition = {
          type: 'state',
          name: stateDefMatch[1],
          typeName: stateDefMatch[2] || undefined,
        };

        // 检查是否有动作
        const hasEntry = code.includes('entry');
        const hasDo = code.includes('do ');
        const hasExit = code.includes('exit');

        if (hasEntry || hasDo || hasExit) {
          state.actions = {
            entry: hasEntry ? extractAction(code, 'entry') : undefined,
            do: hasDo ? extractAction(code, 'do') : undefined,
            exit: hasExit ? extractAction(code, 'exit') : undefined
          };
        }

        // 检查是否有并行状态
        if (code.includes('parallel')) {
          state.isParallel = true;

          // 提取子状态
          const subStateMatches = code.match(/state\s+([\w\.]+)\s*\{/g);
          if (subStateMatches) {
            state.substates = subStateMatches.map(match => {
              const subName = match.replace(/state\s+/, '').replace(/\s*\{/, '');
              return {
                type: 'state',
                name: subName,
                substates: []
              };
            });
          }
        }
// 检查是否有转换
        else if (code.includes('transition') || code.includes('then state')) {
          const transitions: Transition[] = [];

// 提取transition语句
          const transMatches = code.match(/(?:transition|first)\s+(\w+)(?:\s+accept\s+(\w+))?(?:\s+if\s+([^;]+))?(?:\s+do\s+([^;]+))?(?:\s+then|\s+to)\s+(\w+)/g);

          if (transMatches) {
            transMatches.forEach(transStr => {
              const sourceMatch = transStr.match(/(?:transition|first)\s+(\w+)/);
              const targetMatch = transStr.match(/(?:then|\s+to)\s+(\w+)/);
              const triggerMatch = transStr.match(/accept\s+(\w+)/);
              const guardMatch = transStr.match(/if\s+([^;]+?)(?=\s+do|\s+then|\s+to|$)/);
              const effectMatch = transStr.match(/do\s+([^;]+?)(?=\s+then|\s+to|$)/);

              if (sourceMatch && targetMatch) {
                transitions.push({
                  source: sourceMatch[1],
                  target: targetMatch[1],
                  trigger: triggerMatch ? triggerMatch[1] : undefined,
                  guard: guardMatch ? guardMatch[1] : undefined,
                  effect: effectMatch ? effectMatch[1] : undefined
                });
              }
            });
          }

          if (transitions.length > 0) {
            state.transitions = transitions;
          }
        }

// 添加到解析结果
        parsed.elements.push(state);
      }

      parsedData.value = parsed;
    };

// 提取动作文本
    const extractAction = (code: string, actionType: string): string => {
      const actionMatch = new RegExp(`${actionType}\\s+(?:action\\s+)?(\\w+)`, 'i').exec(code);
      return actionMatch ? actionMatch[1] : 'action';
    };

// 解析结构定义
    const parseStructureDefinition = (code: string) => {
      const parsed: ParsedData = {
        type: 'structureDiagram',
        elements: []
      };

// 提取部件定义 - 区分part def和part用法
      const partDefMatch = code.match(/(?:part|item)\s+def\s+(\w+)/);
      const partUsageMatch = code.match(/(?:part|item)\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);

      let structure: StructureElement;

      if (partDefMatch) {
// 这是一个部件定义
        structure = {
          type: code.includes('item') ? 'item def' : 'part def',
          name: partDefMatch[1],
          stereotype: 'def',
          parts: [],
          ports: [],
          connections: [],
          attributes: []
        };
      } else if (partUsageMatch) {
// 这是一个部件用法
        structure = {
          type: code.includes('item') ? 'item' : 'part',
          name: partUsageMatch[1],
          typeName: partUsageMatch[2] || undefined,
          parts: [],
          ports: [],
          connections: [],
          attributes: []
        };
      } else {
// 未找到有效的结构声明
        return;
      }

// 提取子部件
      const partMatches = code.match(/part\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/g);
      if (partMatches) {
        partMatches.forEach(partStr => {
          const partMatch = partStr.match(/part\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);
          if (partMatch && structure.parts) {
            structure.parts.push({
              type: 'part',
              name: partMatch[1],
              typeName: partMatch[2] || undefined
            });
          }
        });
      }

// 提取端口
      const portMatches = code.match(/port\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/g);
      if (portMatches) {
        portMatches.forEach(portStr => {
          const portMatch = portStr.match(/port\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);
          if (portMatch && structure.ports) {
            structure.ports.push({
              name: portMatch[1],
              typeName: portMatch[2] || undefined,
              isConjugated: portStr.includes('~')
            });
          }
        });
      }

// 提取连接
      const connMatches = code.match(/(?:connect|connection)\s+(\w+(?:\.\w+)?)\s+to\s+(\w+(?:\.\w+)?)/g);
      if (connMatches) {
        connMatches.forEach(connStr => {
          const sourceMatch = connStr.match(/(?:connect|connection)\s+(\w+(?:\.\w+)?)/);
          const targetMatch = connStr.match(/to\s+(\w+(?:\.\w+)?)/);

          if (sourceMatch && targetMatch && structure.connections) {
            structure.connections.push({
              source: sourceMatch[1],
              target: targetMatch[1]
            });
          }
        });
      }

// 提取属性
      const attrMatches = code.match(/attribute\s+(?!def)(\w+)(?:\s*:\s*(\w+))?(?:\s*=\s*([^;]+))?/g);
      if (attrMatches) {
        attrMatches.forEach(attrStr => {
          const attrMatch = attrStr.match(/attribute\s+(?!def)(\w+)(?:\s*:\s*(\w+))?(?:\s*=\s*([^;]+))?/);
          if (attrMatch && structure.attributes) {
            structure.attributes.push({
              name: attrMatch[1],
              typeName: attrMatch[2] || 'Any',
              value: attrMatch[3] || undefined
            });
          }
        });
      }

      parsed.elements.push(structure);
      parsedData.value = parsed;
    };

// 解析动作定义
    const parseActionDefinition = (code: string) => {
      const parsed: ParsedData = {
        type: 'actionDiagram',
        elements: []
      };

// 提取动作定义 - 区分action def和action用法
      const actionDefMatch = code.match(/action\s+def\s+(\w+)/);
      const actionUsageMatch = code.match(/action\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);

      let action: ActionElement;

      if (actionDefMatch) {
// 这是一个动作定义
        action = {
          type: 'action def',
          name: actionDefMatch[1],
          stereotype: 'def',
          parameters: [],
          subactions: [],
          flows: [],
          successions: []
        };
      } else if (actionUsageMatch) {
// 这是一个动作用法
        action = {
          type: 'action',
          name: actionUsageMatch[1],
          typeName: actionUsageMatch[2] || undefined,
          parameters: [],
          subactions: [],
          flows: [],
          successions: []
        };
      } else {
// 未找到有效的动作声明
        return;
      }

// 提取参数
      const paramMatches = code.match(/(?:in|out|inout)\s+(?!def)(\w+)(?:\s*:\s*(\w+))?(?:\s*\[\s*(.+?)\s*\])?/g);
      if (paramMatches) {
        paramMatches.forEach(paramStr => {
          const dirMatch = paramStr.match(/(in|out|inout)/);
          const nameMatch = paramStr.match(/(?:in|out|inout)\s+(?!def)(\w+)/);
          const typeMatch = paramStr.match(/:\s*(\w+)/);
          const multMatch = paramStr.match(/\[\s*(.+?)\s*\]/);

          if (dirMatch && nameMatch && action.parameters) {
            action.parameters.push({
              name: nameMatch[1],
              typeName: typeMatch ? typeMatch[1] : 'Any',
              direction: dirMatch[1],
              multiplicity: multMatch ? multMatch[1] : undefined
            });
          }
        });
      }

// 提取子动作
      const subActionMatches = code.match(/action\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/g);
      if (subActionMatches) {
        subActionMatches.forEach(actStr => {
          const matchText = actionUsageMatch ? actionUsageMatch[0] : '';
          if (actStr === matchText) {
            return; // 跳过定义本身
          }

          const actMatch = actStr.match(/action\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);
          if (actMatch && action.subactions) {
            action.subactions.push({
              type: 'action',
              name: actMatch[1],
              typeName: actMatch[2] || undefined
            });
          }
        });
      }

// 提取控制节点
      const controlMatches = code.match(/(?:fork|join|merge|decide)\s+(\w+)/g);
      if (controlMatches) {
        controlMatches.forEach(ctrlStr => {
          const ctrlMatch = ctrlStr.match(/(fork|join|merge|decide)\s+(\w+)/);
          if (ctrlMatch && action.subactions) {
            action.subactions.push({
              type: 'control',
              name: ctrlMatch[2],
              isControl: true,
              controlType: ctrlMatch[1]
            });
          }
        });
      }

// 提取流
      const flowMatches = code.match(/flow\s+(\w+(?:\.\w+)?)\s+to\s+(\w+(?:\.\w+)?)/g);
      if (flowMatches) {
        flowMatches.forEach(flowStr => {
          const sourceMatch = flowStr.match(/flow\s+(\w+(?:\.\w+)?)/);
          const targetMatch = flowStr.match(/to\s+(\w+(?:\.\w+)?)/);

          if (sourceMatch && targetMatch && action.flows) {
            action.flows.push({
              source: sourceMatch[1],
              target: targetMatch[1]
            });
          }
        });
      }

// 提取继承关系
      const succMatches = code.match(/(?:first|then)\s+(\w+)(?:\s+if\s+([^;]+))?(?:\s+then|\s+to)\s+(\w+)/g);
      if (succMatches) {
        succMatches.forEach(succStr => {
          const sourceMatch = succStr.match(/(?:first|then)\s+(\w+)/);
          const targetMatch = succStr.match(/(?:then|\s+to)\s+(\w+)/);
          const guardMatch = succStr.match(/if\s+([^;]+?)(?=\s+then|\s+to|$)/);

          if (sourceMatch && targetMatch && action.successions) {
            action.successions.push({
              source: sourceMatch[1],
              target: targetMatch[1],
              guard: guardMatch ? guardMatch[1] : undefined
            });
          }
        });
      }

      parsed.elements.push(action);
      parsedData.value = parsed;
    };

// 解析需求定义
    const parseRequirementDefinition = (code: string) => {
      const parsed: ParsedData = {
        type: 'requirementDiagram',
        elements: []
      };

// 提取需求定义 - 区分requirement def和requirement用法
      const reqDefMatch = code.match(/requirement\s+def\s+(?:<([^>]+)>)?\s*(\w+)/);
      const reqUsageMatch = code.match(/requirement\s+(?!def)(?:<([^>]+)>)?\s*(\w+)(?:\s*:\s*(\w+))?/);

      let requirement: RequirementElement;

      if (reqDefMatch) {
// 这是一个需求定义
        requirement = {
          type: 'requirement def',
          name: reqDefMatch[2],
          id: reqDefMatch[1] || undefined,
          stereotype: 'def',
          subrequirements: []
        };
      } else if (reqUsageMatch) {
// 这是一个需求用法
        requirement = {
          type: 'requirement',
          name: reqUsageMatch[2],
          id: reqUsageMatch[1] || undefined,
          typeName: reqUsageMatch[3] || undefined,
          subrequirements: []
        };
      } else {
// 未找到有效的需求声明
        return;
      }

// 提取需求文本（从doc注释中）
      const docMatch = code.match(/doc\s*\/\*\s*([\s\S]*?)\s*\*\//);
      if (docMatch) {
        requirement.text = docMatch[1].trim();
      }

// 提取子需求
      const subReqMatches = code.match(/requirement\s+(?!def)(?:<[^>]+>)?\s*(\w+)(?:\s*:\s*(\w+))?/g);
      if (subReqMatches) {
        const mainMatchText = reqUsageMatch ? reqUsageMatch[0] : '';

        subReqMatches.forEach(reqStr => {
          if (reqStr === mainMatchText) {
            return; // 跳过定义本身
          }

          const reqMatch = reqStr.match(/requirement\s+(?!def)(?:<([^>]+)>)?\s*(\w+)(?:\s*:\s*(\w+))?/);
          if (reqMatch && requirement.subrequirements) {
            requirement.subrequirements.push({
              type: 'requirement',
              name: reqMatch[2],
              id: reqMatch[1] || undefined,
              typeName: reqMatch[3] || undefined
            });
          }
        });
      }

// 提取满足关系
      const satisfyMatches = code.match(/satisfy(?:\s+requirement)?\s+(\w+)(?:\s+by\s+(\w+))?/g);
      if (satisfyMatches) {
        requirement.satisfiedBy = satisfyMatches.map(satStr => {
          const satMatch = satStr.match(/satisfy(?:\s+requirement)?\s+(\w+)(?:\s+by\s+(\w+))?/);
          return satMatch ? (satMatch[2] || 'unspecified') : 'unspecified';
        });
      }

// 提取验证关系
      const verifyMatches = code.match(/verify(?:\s+requirement)?\s+(\w+)/g);
      if (verifyMatches) {
        requirement.verifiedBy = verifyMatches.map(verStr => {
          const verMatch = verStr.match(/verify(?:\s+requirement)?\s+(\w+)/);
          return verMatch ? verMatch[1] : 'unspecified';
        });
      }

      parsed.elements.push(requirement);
      parsedData.value = parsed;
    };

// 解析用例定义
    const parseUseCaseDefinition = (code: string) => {
      const parsed: ParsedData = {
        type: 'useCaseDiagram',
        elements: []
      };

// 提取用例定义 - 区分use case def和use case用法
      const ucDefMatch = code.match(/use\s+case\s+def\s+(\w+)/);
      const ucUsageMatch = code.match(/use\s+case\s+(?!def)(\w+)(?:\s*:\s*(\w+))?/);

      let useCase: UseCaseElement;

      if (ucDefMatch) {
// 这是一个用例定义
        useCase = {
          type: 'use case def',
          name: ucDefMatch[1],
          stereotype: 'def',
          actors: [],
          includes: [],
          extends: []
        };
      } else if (ucUsageMatch) {
// 这是一个用例用法
        useCase = {
          type: 'use case',
          name: ucUsageMatch[1],
          typeName: ucUsageMatch[2] || undefined,
          actors: [],
          includes: [],
          extends: []
        };
      } else {
// 未找到有效的用例声明
        return;
      }

// 提取主体
      const subjMatch = code.match(/subject\s+(\w+)(?:\s*:\s*(\w+))?/);
      if (subjMatch) {
        useCase.subject = subjMatch[1];
      }

// 提取角色
      const actorMatches = code.match(/actor\s+(\w+)(?:\s*:\s*(\w+))?/g);
      if (actorMatches) {
        actorMatches.forEach(actStr => {
          const actMatch = actStr.match(/actor\s+(\w+)(?:\s*:\s*(\w+))?/);
          if (actMatch && useCase.actors) {
            useCase.actors.push({
              name: actMatch[1],
              typeName: actMatch[2] || undefined
            });
          }
        });
      }

// 提取包含关系
      const includeMatches = code.match(/include(?:\s+use\s+case)?\s+(\w+)/g);
      if (includeMatches) {
        useCase.includes = includeMatches.map(incStr => {
          const incMatch = incStr.match(/include(?:\s+use\s+case)?\s+(\w+)/);
          return incMatch ? incMatch[1] : '';
        }).filter(name => name !== '');
      }

// 提取扩展关系
      const extendMatches = code.match(/extend(?:\s+use\s+case)?\s+(\w+)/g);
      if (extendMatches) {
        useCase.extends = extendMatches.map(extStr => {
          const extMatch = extStr.match(/extend(?:\s+use\s+case)?\s+(\w+)/);
          return extMatch ? extMatch[1] : '';
        }).filter(name => name !== '');
      }

      parsed.elements.push(useCase);
      parsedData.value = parsed;
    };

// 解析定义
    const parseDefinitionDefinition = (code: string) => {
      const parsed: ParsedData = {
        type: 'definitionDiagram',
        elements: []
      };

// 提取定义 - 检测各种def关键字
      const defMatch = code.match(/(?:part|item|action|constraint|requirement|state)\s+def\s+(?:<([^>]+)>)?\s*(\w+)/);
      if (defMatch) {
        const definition: DefinitionElement = {
          type: defMatch[1] ? `${defMatch[0].split(' ')[0]} def` : `def`,
          name: defMatch[2],
          stereotype: 'def',
          specializes: [],
          features: []
        };

// 提取特化关系
        const specMatch = code.match(/(?:specializes|:>)\s+([\w\s,]+)/);
        if (specMatch) {
          definition.specializes = specMatch[1].split(',').map(s => s.trim());
        }

// 提取特征 - 确保排除def关键字
        const featureMatches = code.match(/(?:attribute|part|item|port|action|state)\s+(?!def)(\w+)(?:\s*:\s*(\w+))?(?:\s*\[\s*(.+?)\s*\])?/g);
        if (featureMatches) {
          featureMatches.forEach(featStr => {
            const typeMatch = featStr.match(/(attribute|part|item|port|action|state)/);
            const nameMatch = featStr.match(/(?:attribute|part|item|port|action|state)\s+(?!def)(\w+)/);
            const defMatch = featStr.match(/:\s*(\w+)/);
            const multMatch = featStr.match(/\[\s*(.+?)\s*\]/);

            if (typeMatch && nameMatch && definition.features) {
              definition.features.push({
                type: typeMatch[1],
                name: nameMatch[1],
                typeName: defMatch ? defMatch[1] : undefined,
                multiplicity: multMatch ? multMatch[1] : undefined
              });
            }
          });
        }

        parsed.elements.push(definition);
      }

      parsedData.value = parsed;
    };

// 当前需要渲染的图组件
    const currentDiagram = computed(() => {
      if (!parsedData.value) {
        return EmptyDiagram;
      }

      switch (selectedDiagramType.value) {
        case 'stateDiagram':
          return StateDiagram;
        case 'structureDiagram':
          return StructureDiagram;
        case 'actionDiagram':
          return ActionDiagram;
        case 'requirementDiagram':
          return RequirementDiagram;
        case 'useCaseDiagram':
          return UseCaseDiagram;
        case 'definitionDiagram':
          return DefinitionDiagram;
        default:
          return EmptyDiagram;
      }
    });

    expose({
      outputDiagram
    })

    return {
      sysmlCode,
      selectedDiagramType,
      parsedData,
      currentDiagram,
      parseAndRender,
      scale,
      fitToView,
      zoomIn,
      zoomOut,
      resetView,
      handleWheel
    };
  }
});

// 空图表组件
const EmptyDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: false
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    return h('div', { class: 'flex items-center justify-center h-64' }, [
      h('p', { class: 'text-gray-500' }, '请选择图表类型并输入SysMLv2代码，然后点击生成图表')
    ]);
  }
});

// 状态图
const StateDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的状态数据');
    }

    const state = data.elements[0] as StateDefinition;
    const hasActions = state.actions && (state.actions.entry || state.actions.do || state.actions.exit);
    const hasSubstates = state.substates && state.substates.length > 0;
    const hasTransitions = state.transitions && state.transitions.length > 0;
    const isParallel = state.isParallel;

// 调整viewBox以适应不同复杂度的图
    let viewBox = '0 0 300 150'; // 默认大小

    if (hasSubstates || hasTransitions) {
      viewBox = '0 0 500 400'; // 较大复杂图
    } else if (hasActions) {
      viewBox = '0 0 350 250'; // 中等大小图
    }

    if (isParallel) {
// 为并行状态提供足够空间
      const numRegions = state.substates?.length || 2;
      const height = Math.max(400, 150 + numRegions * 150);
      viewBox = `0 0 600 ${height}`;
    }

    if (hasSubstates && isParallel) {
// 渲染并行状态图
      return h('svg', { class: 'w-full', viewBox, style: 'max-height: none;' }, [
// 主容器状态
        h('rect', { x: 50, y: 25, width: 400, height: 350, rx: 10, ry: 10, fill: 'white', stroke: 'black', 'stroke-width': 2 }),
        h('line', { x1: 50, y1: 55, x2: 450, y2: 55, stroke: 'black', 'stroke-width': 1 }),
        h('text', { x: 250, y: 45, 'text-anchor': 'middle', 'font-style': 'italic' }, '«state»'),
        h('text', { x: 250, y: 75, 'text-anchor': 'middle', 'font-weight': 'bold' }, state.name),
        h('text', { x: 250, y: 95, 'text-anchor': 'middle', 'font-style': 'italic' }, '«parallel»'),

// 并行状态1
        ...this.renderParallelStateRegions(state)
      ]);
    } else if (hasSubstates || hasTransitions) {
// 渲染带转换的状态图
      return h('svg', { class: 'w-full', viewBox, style: 'max-height: none;' }, [
// 容器状态
        h('rect', { x: 100, y: 25, width: 200, height: 250, rx: 10, ry: 10, fill: 'white', stroke: 'black', 'stroke-width': 2 }),
        h('line', { x1: 100, y1: 55, x2: 300, y2: 55, stroke: 'black', 'stroke-width': 1 }),
        h('text', { x: 200, y: 45, 'text-anchor': 'middle', 'font-style': 'italic' }, '«state»'),
        h('text', { x: 200, y: 75, 'text-anchor': 'middle', 'font-weight': 'bold' }, state.name),

// 初始状态节点
        h('circle', { cx: 130, cy: 110, r: 10, fill: 'black' }),

// 渲染状态和转换
        ...this.renderStatesAndTransitions(state)
      ]);
    } else if (hasActions) {
// 渲染带动作的状态图
      return h('svg', { class: 'w-full', viewBox, style: 'max-height: none;' }, [
        h('rect', { x: 75, y: 25, width: 150, height: 150, rx: 10, ry: 10, fill: 'white', stroke: 'black', 'stroke-width': 2 }),
        h('line', { x1: 75, y1: 55, x2: 225, y2: 55, stroke: 'black', 'stroke-width': 1 }),
        h('line', { x1: 75, y1: 85, x2: 225, y2: 85, stroke: 'black', 'stroke-width': 1 }),
        h('text', { x: 150, y: 45, 'text-anchor': 'middle', 'font-style': 'italic' }, '«state»'),
        h('text', { x: 150, y: 75, 'text-anchor': 'middle', 'font-weight': 'bold' }, state.name),
        h('text', { x: 150, y: 105, 'text-anchor': 'middle', 'font-style': 'italic' }, 'actions'),

// 渲染动作
        ...(state.actions?.entry ? [
          h('text', { x: 90, y: 125, 'text-anchor': 'start' }, 'entry'),
          h('text', { x: 140, y: 125, 'text-anchor': 'start' }, state.actions.entry)
        ] : []),

        ...(state.actions?.do ? [
          h('text', { x: 90, y: 145, 'text-anchor': 'start' }, 'do'),
          h('text', { x: 140, y: 145, 'text-anchor': 'start' }, state.actions.do)
        ] : []),

        ...(state.actions?.exit ? [
          h('text', { x: 90, y: 165, 'text-anchor': 'start' }, 'exit'),
          h('text', { x: 140, y: 165, 'text-anchor': 'start' }, state.actions.exit)
        ] : [])
      ]);
    } else {
// 渲染简单状态图
      return h('svg', { class: 'w-full', viewBox, style: 'max-height: none;' }, [
        h('rect', { x: 75, y: 25, width: 150, height: 100, rx: 10, ry: 10, fill: 'white', stroke: 'black', 'stroke-width': 2 }),
        h('line', { x1: 75, y1: 55, x2: 225, y2: 55, stroke: 'black', 'stroke-width': 1 }),
        h('text', { x: 150, y: 45, 'text-anchor': 'middle', 'font-style': 'italic' }, '«state»'),
        h('text', { x: 150, y: 100, 'text-anchor': 'middle', 'font-weight': 'bold' },
            state.name + (state.typeName ? ' : ' + state.typeName : ''))
      ]);
    }
  },
  methods: {
    renderParallelStateRegions(state: StateDefinition) {
      const elements = [];
      if (state.substates && state.substates.length > 0) {
        const numRegions = state.substates.length;
        const regionHeight = 100;
        const gapBetweenRegions = 20;

        let yOffset = 120;

        state.substates.forEach((substate, index) => {
// 区域容器
          elements.push(
              h('rect', {
                x: 75,
                y: yOffset,
                width: 350,
                height: regionHeight,
                rx: 5,
                ry: 5,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: 150,
                y: yOffset + 20,
                'text-anchor': 'middle',
                'font-style': 'italic'
              }, `«state» ${substate.name}`)
          );

// 初始状态节点
          elements.push(
              h('circle', { cx: 100, cy: yOffset + 50, r: 8, fill: 'black' }),
              h('line', {
                x1: 108,
                y1: yOffset + 50,
                x2: 140,
                y2: yOffset + 50,
                stroke: 'black',
                'stroke-width': 1.5,
                'marker-end': 'url(#arrowhead)'
              })
          );

// 区域子状态
          elements.push(
              h('rect', {
                x: 140,
                y: yOffset + 30,
                width: 200,
                height: 40,
                rx: 5,
                ry: 5,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: 240,
                y: yOffset + 55,
                'text-anchor': 'middle',
                'font-style': 'italic'
              }, `«state» ${substate.name}.1`)
          );

          yOffset += regionHeight + gapBetweenRegions;
        });

// 箭头定义
        elements.push(
            h('defs', {}, [
              h('marker', {
                id: 'arrowhead',
                markerWidth: 10,
                markerHeight: 7,
                refX: 10,
                refY: 3.5,
                orient: 'auto'
              }, [
                h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'black' })
              ])
            ])
        );
      }

      return elements;
    },

    renderStatesAndTransitions(state: StateDefinition) {
      const elements = [];

      if (state.transitions) {
// 假设最多两个状态（示例简化处理）
// 状态1
        elements.push(
            h('rect', {
              x: 160,
              y: 90,
              width: 80,
              height: 40,
              rx: 5,
              ry: 5,
              fill: 'white',
              stroke: 'black',
              'stroke-width': 1.5
            }),
            h('text', {
              x: 200,
              y: 115,
              'text-anchor': 'middle'
            }, state.transitions[0]?.source || 'state1')
        );

// 初始状态到状态1的转换
        elements.push(
            h('line', {
              x1: 140,
              y1: 110,
              x2: 160,
              y2: 110,
              stroke: 'black',
              'stroke-width': 1.5,
              'marker-end': 'url(#arrowhead)'
            })
        );

// 状态1到状态2的转换
        if (state.transitions[0]?.target) {
          elements.push(
              h('line', {
                x1: 200,
                y1: 130,
                x2: 200,
                y2: 170,
                stroke: 'black',
                'stroke-width': 1.5,
                'marker-end': 'url(#arrowhead)'
              })
          );

// 转换标签
          const transitionLabel = [];
          if (state.transitions[0].trigger) transitionLabel.push(state.transitions[0].trigger);
          if (state.transitions[0].guard) transitionLabel.push(`[${state.transitions[0].guard}]`);
          if (state.transitions[0].effect) transitionLabel.push(`/${state.transitions[0].effect}`);

          elements.push(
              h('text', {
                x: 320,
                y: 150,
                'text-anchor': 'start',
                'font-size': 10
              }, transitionLabel.join(''))
          );

// 状态2
          elements.push(
              h('rect', {
                x: 160,
                y: 170,
                width: 80,
                height: 40,
                rx: 5,
                ry: 5,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: 200,
                y: 195,
                'text-anchor': 'middle'
              }, state.transitions[0].target)
          );

// 结束状态节点
          elements.push(
              h('circle', { cx: 200, cy: 250, r: 15, fill: 'white', stroke: 'black', 'stroke-width': 1.5 }),
              h('circle', { cx: 200, cy: 250, r: 10, fill: 'black' }),

// 状态2到结束状态的转换
              h('line', {
                x1: 200,
                y1: 210,
                x2: 200,
                y2: 235,
                stroke: 'black',
                'stroke-width': 1.5,
                'marker-end': 'url(#arrowhead)'
              })
          );
        }
      }

// 箭头定义
      elements.push(
          h('defs', {}, [
            h('marker', {
              id: 'arrowhead',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'black' })
            ])
          ])
      );

      return elements;
    }
  }
});

// 结构图
const StructureDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的结构数据');
    }

    const structure = data.elements[0] as StructureElement;
    const hasParts = structure.parts && structure.parts.length > 0;
    const hasPorts = structure.ports && structure.ports.length > 0;
    const hasConnections = structure.connections && structure.connections.length > 0;
    const hasAttributes = structure.attributes && structure.attributes.length > 0;

// 计算合适的viewBox大小
    let width = 500;
    let height = 400;

    if (hasParts) {
      width = Math.max(width, 100 + structure.parts.length * 200);
    }

    if (hasAttributes) {
      height += structure.attributes.length * 30;
    }

    if (hasConnections && hasParts && structure.parts.length > 2) {
      width += 150;
      height += 100;
    }

// 最小尺寸保证
    width = Math.max(500, width);
    height = Math.max(300, height);

// 绘制结构图
    return h('svg', {
      class: 'w-full',
      viewBox: `0 0 ${width} ${height}`,
      style: 'max-height: none;'
    }, [
// 主容器
      h('rect', {
        x: 50,
        y: 20,
        width: 400,
        height: 360,
        rx: 5,
        ry: 5,
        fill: 'white',
        stroke: 'black',
        'stroke-width': 2
      }),

// 标题栏
      h('rect', {
        x: 50,
        y: 20,
        width: 400,
        height: 30,
        rx: 5,
        ry: 5,
        fill: '#f0f0f0',
        stroke: 'black',
        'stroke-width': 2
      }),

// 名称和类型
      h('text', {
        x: 250,
        y: 40,
        'text-anchor': 'middle',
        'font-weight': 'bold'
      }, `${structure.name}${structure.typeName ? ' : ' + structure.typeName : ''}`),

// 渲染内部结构
      ...this.renderInternalStructure(structure)
    ]);
  },
  methods: {
    renderInternalStructure(structure: StructureElement) {
      const elements = [];
      const hasParts = structure.parts && structure.parts.length > 0;
      const hasPorts = structure.ports && structure.ports.length > 0;
      const hasConnections = structure.connections && structure.connections.length > 0;

// 渲染端口（如果有）
      if (hasPorts) {
        structure.ports.forEach((port, index) => {
// 在容器边界上均匀分布端口
          const portX = index % 2 === 0 ? 50 : 450;
          const portY = 80 + (index * 40);

          elements.push(
              h('rect', {
                x: portX - (index % 2 === 0 ? 0 : 20),
                y: portY - 10,
                width: 20,
                height: 20,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: portX + (index % 2 === 0 ? -25 : 25),
                y: portY + 5,
                'text-anchor': index % 2 === 0 ? 'end' : 'start',
                'font-size': 10
              }, port.name)
          );
        });
      }

// 渲染部件（如果有）
      if (hasParts) {
        structure.parts.forEach((part, index) => {
          const partX = 100 + (index % 2) * 200;
          const partY = 100 + Math.floor(index / 2) * 100;

          elements.push(
              h('rect', {
                x: partX,
                y: partY,
                width: 150,
                height: 80,
                rx: 3,
                ry: 3,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: partX + 75,
                y: partY + 20,
                'text-anchor': 'middle',
                'font-style': 'italic'
              }, '«part»'),
              h('text', {
                x: partX + 75,
                y: partY + 45,
                'text-anchor': 'middle',
                'font-weight': 'bold'
              }, part.name + (part.typeName ? ' : ' + part.typeName : ''))
          );
        });
      }

// 渲染连接（如果有）
      if (hasConnections) {
        structure.connections.forEach((conn, index) => {
// 简化：假设连接是从第一个部件到第二个部件
          if (structure.parts && structure.parts.length >= 2) {
            const part1X = 175; // 第一个部件中心
            const part1Y = 140;
            const part2X = 375; // 第二个部件中心
            const part2Y = 140;

            elements.push(
                h('line', {
                  x1: part1X + 75,
                  y1: part1Y,
                  x2: part2X,
                  y2: part2Y,
                  stroke: 'black',
                  'stroke-width': 1.5,
                  'stroke-dasharray': '5,5',
                  'marker-end': 'url(#arrowhead)'
                }),
                h('text', {
                  x: (part1X + 75 + part2X) / 2,
                  y: (part1Y + part2Y) / 2 - 10,
                  'text-anchor': 'middle',
                  'font-size': 10
                }, conn.name || 'connect')
            );
          }
        });
      }

// 渲染属性区域（如果有）
      if (structure.attributes && structure.attributes.length > 0) {
        elements.push(
            h('rect', {
              x: 75,
              y: 250,
              width: 350,
              height: 100,
              fill: 'white',
              stroke: 'black',
              'stroke-width': 1
            }),
            h('text', {
              x: 250,
              y: 270,
              'text-anchor': 'middle',
              'font-style': 'italic'
            }, 'attributes')
        );

        structure.attributes.forEach((attr, index) => {
          elements.push(
              h('text', {
                x: 100,
                y: 290 + index * 20,
                'text-anchor': 'start',
                'font-size': 12
              }, `${attr.name} : ${attr.typeName}${attr.value ? ' = ' + attr.value : ''}`)
          );
        });
      }

// 箭头定义
      elements.push(
          h('defs', {}, [
            h('marker', {
              id: 'arrowhead',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'black' })
            ])
          ])
      );

      return elements;
    }
  }
});

// 活动图
const ActionDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的活动数据');
    }

    const action = data.elements[0] as ActionElement;
    const hasSubactions = action.subactions && action.subactions.length > 0;
    const hasFlows = action.flows && action.flows.length > 0;
    const hasSuccessions = action.successions && action.successions.length > 0;
    const hasParameters = action.parameters && action.parameters.length > 0;

// 计算合适的viewBox大小
    let width = 600;
    let height = 500;

    if (hasSubactions) {
// 为每个子活动增加高度
      height = Math.max(height, 100 + (action.subactions.length + 2) * 80);
    }

    if (hasFlows && hasSubactions) {
// 流程图需要更多水平空间
      width = Math.max(width, 700);
    }

    if (hasParameters && action.parameters.length > 2) {
// 参数多时增加高度
      height += (action.parameters.length - 2) * 20;
    }

// 最小尺寸保证
    width = Math.max(600, width);
    height = Math.max(400, height);

// 绘制活动图
    return h('svg', {
      class: 'w-full',
      viewBox: `0 0 ${width} ${height}`,
      style: 'max-height: none;'
    }, [
// 容器框
      h('rect', {
        x: 50,
        y: 20,
        width: 500,
        height: 460,
        rx: 5,
        ry: 5,
        fill: 'white',
        stroke: 'black',
        'stroke-width': 1
      }),

// 标题
      h('text', {
        x: 300,
        y: 40,
        'text-anchor': 'middle',
        'font-weight': 'bold',
        'font-size': 16
      }, `${action.name}${action.typeName ? ' : ' + action.typeName : ''}`),

// 参数区域（如果有）
      ...(hasParameters ? this.renderParameters(action) : []),

// 渲染活动图内容
      ...this.renderActionContent(action)
    ]);
  },
  methods: {
    renderParameters(action: ActionElement) {
      const elements = [];

// 参数标题
      elements.push(
          h('text', {
            x: 100,
            y: 70,
            'text-anchor': 'start',
            'font-weight': 'bold'
          }, 'Parameters:')
      );

// 渲染每个参数
      if (action.parameters) {
        action.parameters.forEach((param, index) => {
          elements.push(
              h('text', {
                x: 120,
                y: 90 + index * 20,
                'text-anchor': 'start'
              }, `${param.direction} ${param.name} : ${param.typeName}${param.multiplicity ? ' [' + param.multiplicity + ']' : ''}`)
          );
        });
      }

      return elements;
    },

    renderActionContent(action: ActionElement) {
      const elements = [];
      const startY = action.parameters && action.parameters.length > 0 ?
          110 + action.parameters.length * 20 : 80;

// 开始节点
      elements.push(
          h('circle', {
            cx: 100,
            cy: startY,
            r: 15,
            fill: 'black',
            stroke: 'none'
          }),
          h('text', {
            x: 100,
            y: startY - 20,
            'text-anchor': 'middle',
            'font-size': 12
          }, 'start')
      );

// 活动、控制节点和流
      if (action.subactions) {
        const nodePositions = new Map(); // 记录节点位置

// 首先定位所有节点
        let currentY = startY + 50;

// 记录起始节点位置
        nodePositions.set('start', { x: 100, y: startY });

// 记录子活动节点位置
        action.subactions.forEach((subaction, index) => {
          const x = 300;
          const y = currentY;

          if (subaction.isControl) {
// 控制节点使用不同形状
            switch (subaction.controlType) {
              case 'fork':
              case 'join':
                elements.push(
                    h('rect', {
                      x: x - 40,
                      y: y - 5,
                      width: 80,
                      height: 10,
                      fill: 'black',
                      stroke: 'none'
                    }),
                    h('text', {
                      x: x,
                      y: y - 15,
                      'text-anchor': 'middle',
                      'font-size': 12
                    }, subaction.controlType + ' ' + subaction.name)
                );
                break;
              case 'merge':
              case 'decide':
                elements.push(
                    h('polygon', {
                      points: `${x-15},${y} ${x},${y-15} ${x+15},${y} ${x},${y+15}`,
                      fill: 'white',
                      stroke: 'black',
                      'stroke-width': 1.5
                    }),
                    h('text', {
                      x: x,
                      y: y - 25,
                      'text-anchor': 'middle',
                      'font-size': 12
                    }, subaction.controlType + ' ' + subaction.name)
                );
                break;
            }
          } else {
// 普通活动节点
            elements.push(
                h('rect', {
                  x: x - 60,
                  y: y - 20,
                  width: 120,
                  height: 40,
                  rx: 15,
                  ry: 15,
                  fill: 'white',
                  stroke: 'black',
                  'stroke-width': 1.5
                }),
                h('text', {
                  x: x,
                  y: y + 5,
                  'text-anchor': 'middle'
                }, subaction.name)
            );
          }

// 记录节点位置
          nodePositions.set(subaction.name, { x, y });

          currentY += 80;
        });

// 添加终止节点
        const endY = currentY;
        elements.push(
            h('circle', {
              cx: 100,
              cy: endY,
              r: 15,
              fill: 'white',
              stroke: 'black',
              'stroke-width': 1.5
            }),
            h('circle', {
              cx: 100,
              cy: endY,
              r: 10,
              fill: 'black'
            }),
            h('text', {
              x: 100,
              y: endY - 25,
              'text-anchor': 'middle',
              'font-size': 12
            }, 'end')
        );
        nodePositions.set('end', { x: 100, y: endY });

// 然后绘制连接线
        if (action.successions) {
          action.successions.forEach(succession => {
            const source = nodePositions.get(succession.source);
            const target = nodePositions.get(succession.target);

            if (source && target) {
              elements.push(
                  h('line', {
                    x1: source.x,
                    y1: source.y + (source.y < target.y ? 20 : -20),
                    x2: target.x,
                    y2: target.y - (target.y > source.y ? 20 : -20),
                    stroke: 'black',
                    'stroke-width': 1.5,
                    'marker-end': 'url(#arrowhead)'
                  })
              );

// 如果有守卫条件
              if (succession.guard) {
                const midX = (source.x + target.x) / 2;
                const midY = (source.y + target.y) / 2;

                elements.push(
                    h('text', {
                      x: midX + 20,
                      y: midY - 5,
                      'text-anchor': 'start',
                      'font-size': 10,
                      'font-style': 'italic'
                    }, `[${succession.guard}]`)
                );
              }
            }
          });
        }
// 如果没有明确的succession，使用默认连接
        else if (action.subactions.length > 0) {
// 从start连接到第一个活动
          const firstAction = nodePositions.get(action.subactions[0].name);
          if (firstAction) {
            elements.push(
                h('line', {
                  x1: 100,
                  y1: startY + 15,
                  x2: firstAction.x,
                  y2: firstAction.y - 20,
                  stroke: 'black',
                  'stroke-width': 1.5,
                  'marker-end': 'url(#arrowhead)'
                })
            );
          }

// 从最后一个活动连接到end
          const lastAction = nodePositions.get(action.subactions[action.subactions.length - 1].name);
          if (lastAction) {
            elements.push(
                h('line', {
                  x1: lastAction.x,
                  y1: lastAction.y + 20,
                  x2: 100,
                  y2: endY - 15,
                  stroke: 'black',
                  'stroke-width': 1.5,
                  'marker-end': 'url(#arrowhead)'
                })
            );
          }
        }

// 渲染流
        if (action.flows) {
          action.flows.forEach(flow => {
// 简化：假设流是从一个子活动到另一个（不处理嵌套属性）
            const source = flow.source.split('.')[0];
            const target = flow.target.split('.')[0];

            const sourcePos = nodePositions.get(source);
            const targetPos = nodePositions.get(target);

            if (sourcePos && targetPos) {
              elements.push(
                  h('path', {
                    d: `M ${sourcePos.x + 60} ${sourcePos.y} C ${sourcePos.x + 100} ${sourcePos.y}, ${targetPos.x - 100} ${targetPos.y}, ${targetPos.x - 60} ${targetPos.y}`,
                    fill: 'none',
                    stroke: '#336699',
                    'stroke-width': 2,
                    'stroke-dasharray': '5,2',
                    'marker-end': 'url(#flowArrow)'
                  }),
                  h('text', {
                    x: (sourcePos.x + targetPos.x) / 2,
                    y: (sourcePos.y + targetPos.y) / 2 - 10,
                    'text-anchor': 'middle',
                    'font-size': 10,
                    fill: '#336699'
                  }, flow.payload || 'flow')
              );
            }
          });
        }
      }

// 添加箭头定义
      elements.push(
          h('defs', {}, [
            h('marker', {
              id: 'arrowhead',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'black' })
            ]),
            h('marker', {
              id: 'flowArrow',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: '#336699' })
            ])
          ])
      );

      return elements;
    }
  }
});

// 需求图
const RequirementDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的需求数据');
    }

    const requirement = data.elements[0] as RequirementElement;
    const hasSubrequirements = requirement.subrequirements && requirement.subrequirements.length > 0;
    const hasSatisfiers = requirement.satisfiedBy && requirement.satisfiedBy.length > 0;

// 计算适当的视图尺寸
    let width = 600;
    let height = 400;

    if (hasSubrequirements) {
// 为子需求增加大小
      width = Math.max(width, 200 + requirement.subrequirements.length * 220);
      height = Math.max(height, 500);
    }

    if (hasSatisfiers) {
// 为满足关系增加高度
      height += requirement.satisfiedBy.length * 80;
    }

// 确保最小尺寸
    width = Math.max(600, width);
    height = Math.max(400, height);

// 绘制需求图
    return h('svg', {
      class: 'w-full',
      viewBox: `0 0 ${width} ${height}`,
      style: 'max-height: none;'
    }, [
// 渲染需求图内容
      ...this.renderRequirement(requirement, 300, 100, 0)
    ]);
  },
  methods: {
    renderRequirement(req: RequirementElement, x: number, y: number, level: number) {
      const elements = [];
      const width = 200;
      const height = req.text ? 120 : 80;

// 需求框
      elements.push(
          h('rect', {
            x: x - width / 2,
            y,
            width,
            height,
            rx: 10,
            ry: 10,
            fill: 'white',
            stroke: '#3366cc',
            'stroke-width': 2
          })
      );

// 标题栏底色
      elements.push(
          h('rect', {
            x: x - width / 2,
            y,
            width,
            height: 30,
            rx: 10,
            ry: 10,
            fill: '#e6eeff',
            stroke: '#3366cc',
            'stroke-width': 2
          })
      );

// 标题文本
      elements.push(
          h('text', {
            x,
            y: y + 20,
            'text-anchor': 'middle',
            'font-weight': 'bold',
            fill: '#3366cc'
          }, '«requirement»')
      );

// ID和名称
      elements.push(
          h('text', {
            x,
            y: y + 50,
            'text-anchor': 'middle',
            'font-weight': 'bold'
          }, `${req.id ? req.id + ': ' : ''}${req.name}`)
      );

// 需求文本（如果有）
      if (req.text) {
        elements.push(
            h('foreignObject', {
              x: x - width / 2 + 10,
              y: y + 60,
              width: width - 20,
              height: 50
            }, [
              h('div', {
                xmlns: 'http://www.w3.org/1999/xhtml',
                style: {
                  fontSize: '12px',
                  fontStyle: 'italic',
                  textAlign: 'center',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }
              }, req.text)
            ])
        );
      }

// 渲染子需求（如果有）
      if (req.subrequirements && req.subrequirements.length > 0) {
        const numSubreqs = req.subrequirements.length;
        const totalWidth = numSubreqs * 220;
        let startX = x - totalWidth / 2 + 110;

        req.subrequirements.forEach((subreq, index) => {
          const subX = startX + index * 220;
          const subY = y + height + 80;

// 添加连接线
          elements.push(
              h('line', {
                x1: x,
                y1: y + height,
                x2: subX,
                y2: subY,
                stroke: '#3366cc',
                'stroke-width': 1.5,
                'stroke-dasharray': '5,5',
                'marker-end': 'url(#reqArrow)'
              })
          );

// 递归渲染子需求
          elements.push(...this.renderRequirement(subreq, subX, subY, level + 1));
        });
      }

// 渲染满足关系（如果有）
      if (req.satisfiedBy && req.satisfiedBy.length > 0) {
        const satisfyY = y + height + 120;

        req.satisfiedBy.forEach((satisfier, index) => {
          elements.push(
// 满足元素图形
              h('rect', {
                x: x - 50,
                y: satisfyY,
                width: 100,
                height: 50,
                rx: 5,
                ry: 5,
                fill: 'white',
                stroke: 'green',
                'stroke-width': 1.5
              }),

// 满足元素名称
              h('text', {
                x,
                y: satisfyY + 30,
                'text-anchor': 'middle'
              }, satisfier),

// 满足关系线
              h('line', {
                x1: x,
                y1: y + height,
                x2: x,
                y2: satisfyY,
                stroke: 'green',
                'stroke-width': 1.5,
                'stroke-dasharray': '5,5',
                'marker-end': 'url(#satisfyArrow)'
              }),

// 标签
              h('text', {
                x: x + 10,
                y: y + height + (satisfyY - (y + height)) / 2,
                'text-anchor': 'start',
                'font-size': 10,
                fill: 'green',
                'font-style': 'italic'
              }, '«satisfy»')
          );
        });
      }

// 添加箭头定义
      if (level === 0) {
        elements.push(
            h('defs', {}, [
              h('marker', {
                id: 'reqArrow',
                markerWidth: 10,
                markerHeight: 7,
                refX: 10,
                refY: 3.5,
                orient: 'auto'
              }, [
                h('polygon', { points: '0 0, 10 3.5, 0 7', fill: '#3366cc' })
              ]),
              h('marker', {
                id: 'satisfyArrow',
                markerWidth: 10,
                markerHeight: 7,
                refX: 10,
                refY: 3.5,
                orient: 'auto'
              }, [
                h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'green' })
              ])
            ])
        );
      }

      return elements;
    }
  }
});

// 用例图
const UseCaseDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的用例数据');
    }

    const useCase = data.elements[0] as UseCaseElement;
    const hasActors = useCase.actors && useCase.actors.length > 0;
    const hasIncludes = useCase.includes && useCase.includes.length > 0;
    const hasExtends = useCase.extends && useCase.extends.length > 0;

// 计算适当的视图尺寸
    let width = 700;
    let height = 500;

    if (hasActors && useCase.actors.length > 3) {
// 为更多角色增加高度
      height += (useCase.actors.length - 3) * 100;
    }

    if (hasIncludes && useCase.includes.length > 2) {
// 为包含用例增加尺寸
      height += (useCase.includes.length - 2) * 80;
      width = Math.max(width, 800);
    }

    if (hasExtends && useCase.extends.length > 0) {
// 为扩展用例增加尺寸
      width = Math.max(width, 800);
      height += useCase.extends.length * 60;
    }

// 确保最小尺寸
    width = Math.max(700, width);
    height = Math.max(500, height);

// 绘制用例图
    return h('svg', {
      class: 'w-full',
      viewBox: `0 0 ${width} ${height}`,
      style: 'max-height: none;'
    }, [
// 主体系统边界框
      h('rect', {
        x: 200,
        y: 50,
        width: 350,
        height: 400,
        rx: 20,
        ry: 20,
        fill: 'white',
        stroke: '#666',
        'stroke-width': 2,
        'stroke-dasharray': '5,5'
      }),

// 系统名称
      h('text', {
        x: 375,
        y: 80,
        'text-anchor': 'middle',
        'font-weight': 'bold',
        'font-size': 16
      }, useCase.subject || 'System'),

// 渲染用例图内容
      ...this.renderUseCaseContent(useCase)
    ]);
  },
  methods: {
    renderUseCaseContent(useCase: UseCaseElement) {
      const elements = [];

// 绘制主用例
      elements.push(
          h('ellipse', {
            cx: 375,
            cy: 150,
            rx: 80,
            ry: 40,
            fill: 'white',
            stroke: '#3366cc',
            'stroke-width': 2
          }),
          h('text', {
            x: 375,
            y: 155,
            'text-anchor': 'middle',
            'font-weight': 'bold'
          }, useCase.name)
      );

// 绘制包含的用例
      if (useCase.includes && useCase.includes.length > 0) {
        useCase.includes.forEach((include, index) => {
          const x = 375;
          const y = 250 + index * 80;

          elements.push(
// 包含用例椭圆
              h('ellipse', {
                cx: x,
                cy: y,
                rx: 70,
                ry: 35,
                fill: 'white',
                stroke: '#3366cc',
                'stroke-width': 1.5
              }),

// 用例名称
              h('text', {
                x,
                y: y + 5,
                'text-anchor': 'middle'
              }, include),

// 包含关系线
              h('line', {
                x1: 375,
                y1: 190,
                x2: x,
                y2: y - 35,
                stroke: '#3366cc',
                'stroke-width': 1.5,
                'stroke-dasharray': '5,5',
                'marker-end': 'url(#includeArrow)'
              }),

// 标签
              h('text', {
                x: (375 + x) / 2 + 10,
                y: (190 + (y - 35)) / 2,
                'text-anchor': 'start',
                'font-size': 10,
                fill: '#3366cc',
                'font-style': 'italic'
              }, '«include»')
          );
        });
      }

// 绘制继承的用例
      if (useCase.extends && useCase.extends.length > 0) {
        useCase.extends.forEach((extend, index) => {
          const x = 525;
          const y = 150 + index * 80;

          elements.push(
// 扩展用例椭圆
              h('ellipse', {
                cx: x,
                cy: y,
                rx: 70,
                ry: 35,
                fill: 'white',
                stroke: '#3366cc',
                'stroke-width': 1.5
              }),

// 用例名称
              h('text', {
                x,
                y: y + 5,
                'text-anchor': 'middle'
              }, extend),

// 扩展关系线
              h('line', {
                x1: 455,
                y1: 150,
                x2: x - 70,
                y2: y,
                stroke: '#3366cc',
                'stroke-width': 1.5,
                'stroke-dasharray': '5,5',
                'marker-end': 'url(#extendArrow)'
              }),

// 标签
              h('text', {
                x: (455 + (x - 70)) / 2 + 10,
                y: (150 + y) / 2 - 5,
                'text-anchor': 'start',
                'font-size': 10,
                fill: '#3366cc',
                'font-style': 'italic'
              }, '«extend»')
          );
        });
      }

// 绘制角色
      if (useCase.actors && useCase.actors.length > 0) {
        useCase.actors.forEach((actor, index) => {
          const x = 100;
          const y = 100 + index * 100;

// 绘制角色图标（简化为小人图形）
          elements.push(
// 头部
              h('circle', {
                cx: x,
                cy: y - 25,
                r: 10,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),

// 身体
              h('line', {
                x1: x,
                y1: y - 15,
                x2: x,
                y2: y + 15,
                stroke: 'black',
                'stroke-width': 1.5
              }),

// 手臂
              h('line', {
                x1: x - 15,
                y1: y,
                x2: x + 15,
                y2: y,
                stroke: 'black',
                'stroke-width': 1.5
              }),

// 腿部
              h('line', {
                x1: x,
                y1: y + 15,
                x2: x - 10,
                y2: y + 40,
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('line', {
                x1: x,
                y1: y + 15,
                x2: x + 10,
                y2: y + 40,
                stroke: 'black',
                'stroke-width': 1.5
              }),

// 角色名称
              h('text', {
                x,
                y: y + 60,
                'text-anchor': 'middle',
                'font-weight': 'bold'
              }, actor.name),

// 连接到用例
              h('line', {
                x1: x + 20,
                y1: y,
                x2: 295,
                y2: 150,
                stroke: 'black',
                'stroke-width': 1.5
              })
          );
        });
      }

// 箭头定义
      elements.push(
          h('defs', {}, [
            h('marker', {
              id: 'includeArrow',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: '#3366cc' })
            ]),
            h('marker', {
              id: 'extendArrow',
              markerWidth: 10,
              markerHeight: 7,
              refX: 10,
              refY: 3.5,
              orient: 'auto'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: '#3366cc' })
            ])
          ])
      );

      return elements;
    }
  }
});

// 定义图
const DefinitionDiagram = defineComponent({
  props: {
    parsedData: {
      type: Object,
      required: true
    },
    fitToView: {
      type: Boolean,
      default: true
    }
  },
  render() {
    const data = this.parsedData as ParsedData;
    if (!data || !data.elements || data.elements.length === 0) {
      return h('div', { class: 'text-center' }, '没有可渲染的定义数据');
    }

    const definition = data.elements[0] as DefinitionElement;
    const hasSpecializes = definition.specializes && definition.specializes.length > 0;
    const hasFeatures = definition.features && definition.features.length > 0;

// 计算适当的视图尺寸
    let width = 600;
    let height = 500;

    if (hasSpecializes) {
// 为特化关系增加宽度
      width = Math.max(width, 200 + definition.specializes.length * 200);
    }

    if (hasFeatures && definition.features.length > 5) {
// 为更多特征增加高度
      height += (definition.features.length - 5) * 20;
    }

// 确保最小尺寸
    width = Math.max(600, width);
    height = Math.max(500, height);

// 绘制定义图
    return h('svg', {
      class: 'w-full',
      viewBox: `0 0 ${width} ${height}`,
      style: 'max-height: none;'
    }, [
// 渲染定义内容
      ...this.renderDefinition(definition)
    ]);
  },
  methods: {
    renderDefinition(definition: DefinitionElement) {
      const elements = [];
      const mainX = 300;
      const mainY = 150;
      const width = 180;
      const height = 30 + (definition.features ? definition.features.length * 20 : 0) + 40;

// 定义框
      elements.push(
          h('rect', {
            x: mainX - width / 2,
            y: mainY,
            width,
            height,
            rx: 5,
            ry: 5,
            fill: 'white',
            stroke: 'black',
            'stroke-width': 1.5
          })
      );

// 标题
      elements.push(
          h('text', {
            x: mainX,
            y: mainY + 20,
            'text-anchor': 'middle',
            'font-style': 'italic'
          }, `«${definition.type} def»`),
          h('text', {
            x: mainX,
            y: mainY + 40,
            'text-anchor': 'middle',
            'font-weight': 'bold'
          }, definition.name)
      );

// 分隔线
      elements.push(
          h('line', {
            x1: mainX - width / 2,
            y1: mainY + 50,
            x2: mainX + width / 2,
            y2: mainY + 50,
            stroke: 'black',
            'stroke-width': 1
          })
      );

// 渲染特征
      if (definition.features && definition.features.length > 0) {
        definition.features.forEach((feature, index) => {
          elements.push(
              h('text', {
                x: mainX - width / 2 + 10,
                y: mainY + 70 + index * 20,
                'text-anchor': 'start',
                'font-size': 12
              }, `${feature.type} ${feature.name}${feature.typeName ? ' : ' + feature.typeName : ''}${feature.multiplicity ? ' [' + feature.multiplicity + ']' : ''}`)
          );
        });
      }

// 渲染特化关系
      if (definition.specializes && definition.specializes.length > 0) {
        definition.specializes.forEach((superDef, index) => {
          const superX = mainX + (index - (definition.specializes.length - 1) / 2) * 200;
          const superY = 50;

// 超类框
          elements.push(
              h('rect', {
                x: superX - 80,
                y: superY,
                width: 160,
                height: 40,
                rx: 5,
                ry: 5,
                fill: 'white',
                stroke: 'black',
                'stroke-width': 1.5
              }),
              h('text', {
                x: superX,
                y: superY + 25,
                'text-anchor': 'middle',
                'font-weight': 'bold'
              }, superDef)
          );

// 特化关系线
          elements.push(
              h('line', {
                x1: superX,
                y1: superY + 40,
                x2: mainX,
                y2: mainY,
                stroke: 'black',
                'stroke-width': 1.5,
                'marker-start': 'url(#generalization)'
              })
          );
        });
      }

// 箭头定义
      elements.push(
          h('defs', {}, [
            h('marker', {
              id: 'generalization',
              markerWidth: 10,
              markerHeight: 7,
              refX: 1,
              refY: 3.5,
              orient: 'auto',
              markerUnits: 'strokeWidth'
            }, [
              h('polygon', { points: '0 0, 10 3.5, 0 7', fill: 'white', stroke: 'black', 'stroke-width': 1 })
            ])
          ])
      );

      return elements;
    }
  }
});
</script>

<style>
/* Only necessary custom styles, most styling uses Tailwind */
.min-h-64 {
  min-height: 16rem;
}

/* Diagram-specific styles */
svg {
  max-height: none;
  min-width: 300px;
}

.diagram-container {
  transition: transform 0.1s ease-out;
}

/* Tooltip styles for diagram elements */
.tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
}

/* Animation for transitions */
@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}
.animated-dash {
  animation: dash 20s linear infinite;
}

/* Responsive sizing */
@media (max-width: 640px) {
  .min-h-64 {
    min-height: 12rem;
  }
}
</style>
