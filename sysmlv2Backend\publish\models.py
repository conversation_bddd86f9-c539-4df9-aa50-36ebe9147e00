from django.contrib.postgres.fields import Array<PERSON>ield
from django.db import models

# Create your models here.
class Doc(models.Model):
    doc_title = models.Char<PERSON>ield("doc_title", max_length=100, default="default doc title")
    doc_content = models.TextField("doc_content", blank=True, null=True)
    base_dir_id = models.IntegerField("base_dir_id", default=-1)

class Folder(models.Model):
    folder_title = models.CharField("folder_title", max_length=100, default="default folder title")
    base_dir_id = models.IntegerField("base_dir_id", default=-1)

class Project(models.Model):
    project_title = models.Char<PERSON>ield("doc_title", max_length=100, default="default doc title")
    base_user_id = models.IntegerField("base_dir_id", default=-3)
    root_dir_id = models.IntegerField("root_dir_id", default=-2)

class User(models.Model):
    username = models.<PERSON>r<PERSON><PERSON>("username", max_length=100, default="default name")
    password = models.Char<PERSON><PERSON>("password", max_length=100)
