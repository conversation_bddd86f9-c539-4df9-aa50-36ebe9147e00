/* Register.css */
.register-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #f5f7fa;
}

.register-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a73e8;
  color: white;
  padding: 2rem;
}

.register-image {
  max-width: 80%;
  text-align: center;
}

.register-image h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.register-image p {
  font-size: 1.1rem;
  opacity: 0.9;
}

.register-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.register-form-container {
  width: 80%;
  max-width: 450px;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.register-form-container h2 {
  margin-bottom: 1.5rem;
  color: #333;
  text-align: center;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
}

.form-group input:focus {
  outline: none;
  border-color: #1a73e8;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.input-error {
  border-color: #d93025 !important;
}

.error-message {
  color: #d93025;
  font-size: 0.85rem;
  margin-top: 0.3rem;
  margin-bottom: 0;
}

.submit-error {
  text-align: center;
  margin-bottom: 1rem;
}

.register-button {
  width: 100%;
  padding: 0.75rem;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.register-button:hover {
  background-color: #1557b0;
}

.login-link {
  text-align: center;
  margin-top: 1.5rem;
  color: #555;
}

.login-link span {
  color: #1a73e8;
  cursor: pointer;
  text-decoration: underline;
}

/* 注册成功弹窗 */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
}

.modal-content h3 {
  color: #1a73e8;
  margin-bottom: 1rem;
}

.modal-content p {
  color: #555;
}