<template>
  <div class="card-container" @click="jumpIntoProject">
<!--    <el-row>-->
<!--      <el-col :span="23">-->
        <el-icon :size="42" class="project-icon"><Management /></el-icon>
        <span class="project-title">{{projectObj.project_title}}</span>
<!--      </el-col>-->
<!--      <el-col :span="1">-->
        <el-dropdown class="more-options">
          <button class="more-button">
            <el-icon><MoreFilled /></el-icon>
          </button>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="renameVisible = true">重命名</el-dropdown-item>
              <el-dropdown-item>
                <el-popconfirm
                    confirm-button-text="Yes"
                    cancel-button-text="No"
                    :icon="InfoFilled"
                    icon-color="#626AEF"
                    title="您确定删除该图吗?"
                    @confirm="confirmEvent"
                    @cancel="cancelEvent"
                >
                  <template #reference>删除</template>
                </el-popconfirm>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
<!--      </el-col>-->
<!--    </el-row>-->

  </div>

  <el-dialog
      v-model="renameVisible"
      title="重命名图"
      style="max-width: 400px"
  >
    <el-form :model="newProjectForm">
      <el-form-item label="新名称" :label-width="formLabelWidth">
        <el-input v-model="newProjectForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="renameVisible = false">取消</el-button>
        <el-button type="primary" @click="renameDoc(newProjectForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {InfoFilled, Management} from "@element-plus/icons-vue";
import {defineProps, ref} from "vue";
import {useRouter} from "vue-router";
import axios from "axios";
import {ElMessage} from "element-plus";

const iconColor = '#00BBFF';

const router = useRouter();

const props = defineProps({
  projectObj: {
    type: Object,
    required: true,
  },
  intoProject: {
    type: Function,
    required: true,
  },
  reGetProjects: {
    type: Function,
    required: true,
  }
});

const renameVisible = ref(false);
const newProjectForm = ref({
  title: "",
});
const formLabelWidth = "90px";

const jumpIntoProject = () => {
  props.intoProject(props.projectObj)
};

const renameDoc = (newTitle) => {
  renameVisible.value = !renameVisible.value;

  let data = JSON.stringify({
    target_project_id: props.projectObj.id,
    new_project_title: newTitle,
  });

  var config = {
    method: 'post',
    url: '/api/renameProject',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "重命名成功",
            type: "success",
          });
        }

        props.reGetProjects();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const deleteProject = () => {
  //删除文档
  let data = JSON.stringify({
    target_project_id: props.projectObj.id,
  });

  let config = {
    method: 'post',
    url: '/api/deleteProject',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
        }

        props.reGetProjects();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const confirmEvent = () => {
  console.log("confirm!");
  deleteProject();
};
const cancelEvent = () => {
  console.log("cancel!");
};
</script>

<style scoped>
.card-container {
  display: flex;
  align-items: center; /* 垂直居中 */
  height: 42px;
  margin-bottom: 5px;
  cursor: pointer;
}
.card-container:hover {
  background-color: #e2e2e8;
}

.project-title {
  line-height: 42px;
}

.more-options {
  margin-left: auto;
  margin-right: 20px;
}
.more-options:hover {
  background-color: #d1d1d7;
}

.more-button {
  background-color: Transparent;
  border-style: none;
  outline: none;
  cursor: pointer;
}

.project-icon {
  color:rgba(0,191,255,0.91)
}

</style>