# publish/urls.py
from django.urls import path
from .views import *

urlpatterns = [
    path('register', register),  # 指定register函数的路由为register
    path('login', login),

    path('createDoc', create_doc),
    path('createFolder', create_folder),

    path('getElements', get_elements),
    path('getDocContent', get_doc_content),

    path('deleteDoc', delete_doc),
    path('deleteFolder', delete_folder),

    path('renameDoc', rename_doc),
    path('renameFolder', rename_folder),

    path('saveDoc', save_doc),

    path('createProject', create_project),
    path('getProjects', get_projects),
    path('renameProject', rename_project),
    path('deleteProject', delete_project),
]
