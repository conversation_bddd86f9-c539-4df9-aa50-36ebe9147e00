# from django.http import JsonResponse
# from django.views.decorators.csrf import csrf_exempt

import json
from xml.dom.minidom import Document

from django.contrib.auth.hashers import make_password, check_password
from .decorators import *
from .models import *
from .user_token import create_token, get_user_account
from django.forms.models import model_to_dict

# Create your views here.

@check_request_method('POST')    # 跨域设置
def register(request):  # 继承请求类
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求方式错误"})

    data = json.loads(request.body.decode("utf-8"))
    username = data.get('username')  # 获取请求体中的请求数据
    password = data.get('password')
    print('username:', username)
    print('password:',password)

    if User.objects.filter(username=username).exists():
        print("Username already exists")
        return JsonResponse({'code': 2001, 'msg': "用户名已被占用"})

    # 新建 User 对象，赋值用户名和密码并保存
    new_user = User(username=username, password=password)
    new_user.save()  # 一定要save才能保存到数据库中
    print("注册成功")
    return JsonResponse({'code': 0, 'msg': "注册成功"})

@check_request_method('POST')
def login(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求方式错误"})

    data = json.loads(request.body.decode("utf-8"))
    username = data.get('username')  # 获取请求数据
    password = data.get('password')

    if User.objects.filter(username=username).exists() is False:
        return JsonResponse({'code': 2002, 'msg': "用户不存在"})

    user = User.objects.get(username=username)
    token = create_token(user.username)
    res_data = {
        'user_info': model_to_dict(user),
        'token': token,
    }
    if user.password == password:  # 判断请求的密码是否与数据库存储的密码相同
        return JsonResponse({'code': 0, 'msg': "登录成功", 'data': res_data})
    else:
        return JsonResponse({'code': 2003, 'msg': "密码错误"})

def doc_to_dict(doc):
    data = {
        'id': doc.id,
        'doc_title': doc.doc_title,
        'doc_content': doc.doc_content,
        'base_dir_id': doc.base_dir_id,
    }
    return data

@check_request_method('POST')
def get_projects(request):
    # 获取指定用户的项目列表
    data = json.loads(request.body.decode("utf-8"))

    target_user_id = data.get('target_user_id')
    print('target_user_id:', target_user_id)

    project_list = Project.objects.filter(base_user_id=target_user_id)
    return_project_list = [model_to_dict(project) for project in project_list]

    return JsonResponse({'code': 0, 'msg': "查询成功", 'data': return_project_list})

@check_request_method('POST')
def get_elements(request):
    # 获取根目录下的文件和文件夹
    data = json.loads(request.body.decode("utf-8"))
    base_project_id = data.get('base_project_id')
    print("base_project_id:", base_project_id)

    target_project = Project.objects.get(id=base_project_id)
    base_dir_id = target_project.root_dir_id
    doc_list = Doc.objects.filter(base_dir_id=base_dir_id)
    return_doc_list = [model_to_dict(doc) for doc in doc_list]

    return_folder_list = []
    folder_list = Folder.objects.filter(base_dir_id=base_dir_id)
    for folder in folder_list:
        return_folder_list.append(getFolder(folder.id))

    res_data = {
        'root_dir_id': target_project.root_dir_id,
        'folder_list': return_folder_list,
        'doc_list': return_doc_list,
    }
    return JsonResponse({'code': 0, 'msg': "查询成功", 'data': res_data})

def getFolder(target_dir_id):
    # 获取指定文件夹下的文件
    target_folder = Folder.objects.get(id=target_dir_id)
    res_data = {
        'id': target_folder.id,
        'folder_title': target_folder.folder_title,
        'base_dir_id': target_folder.base_dir_id,
        'doc_list': []
    }
    docs = Doc.objects.filter(base_dir_id=target_dir_id)
    for doc in docs:
        res_data['doc_list'].append(model_to_dict(doc))

    return res_data

@check_request_method('POST')
def get_doc_content(request):
    data = json.loads(request.body.decode("utf-8"))
    target_doc_id = data.get('target_doc_id')

    target_doc = Doc.objects.get(id=target_doc_id)
    res_doc_content = target_doc.doc_content

    return JsonResponse({'code': 0, 'msg': "查询成功", 'data': res_doc_content})

def folder_to_dict(folder):
    data = {
        'id': folder.id,
        'folder_title': folder.folder_title,
        'base_dir_id': folder.base_dir_id,
    }
    return data

@check_request_method('POST')
def create_doc(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    title = data.get('title')  # 获取请求体中的请求数据
    base_dir_id = data.get('base_dir_id')

    new_doc = Doc(doc_title=title, base_dir_id=base_dir_id)
    new_doc.save()

    return JsonResponse({'code': 0, 'msg': "新建文件成功"})

@check_request_method('POST')
def create_folder(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    title = data.get('title')
    base_dir_id = data.get('base_dir_id')

    new_dir = Folder(folder_title=title, base_dir_id=base_dir_id)
    new_dir.save()

    return JsonResponse({'code': 0, 'msg': "新建文件夹成功"})

@check_request_method('POST')
def create_project(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    title = data.get('title')
    base_user_id = data.get('base_user_id')

    new_project = Project(project_title=title, base_user_id=base_user_id)

    project_root_dir = Folder(folder_title='project' + '+' + title + '+' + 'root')
    project_root_dir.save()

    new_project.root_dir_id = project_root_dir.id
    new_project.save()
    # new_dir = Folder(folder_title=(username + 'root'))
    # new_dir.save()

    return JsonResponse({'code': 0, 'msg': "新建项目成功"})

@check_request_method('POST')
def save_doc(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})


    data = json.loads(request.body.decode("utf-8"))
    target_doc_id = data.get('target_doc_id')
    content = data.get('content')

    print(target_doc_id)
    print('内容为' + content)

    target_doc = Doc.objects.get(id=target_doc_id)
    target_doc.doc_content = content
    target_doc.save()
    return JsonResponse({'code': 0, 'msg': "保存文件成功"})

@check_request_method('POST')
def delete_doc(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_doc_id = data.get('target_doc_id')

    target_doc = Doc.objects.get(id=target_doc_id)
    target_doc.delete()
    return JsonResponse({'code': 0, 'msg': "删除成功"})

@check_request_method('POST')
def delete_folder(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_folder_id = data.get('target_folder_id')

    target_folder = Folder.objects.get(id=target_folder_id)
    son_doc_list = Doc.objects.filter(base_dir_id=target_folder.id)
    for doc in son_doc_list:
        target_doc = Doc.objects.get(id=doc.id)
        target_doc.delete()

    target_folder.delete()
    return JsonResponse({'code': 0, 'msg': "删除成功"})

def delete_folder_sonList(target_folder_id):
    son_doc_list = Doc.objects.filter(base_dir_id=target_folder_id)
    for doc in son_doc_list:
        target_doc = Doc.objects.get(id=doc.id)
        target_doc.delete()

@check_request_method('POST')
def delete_project(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_project_id = data.get('target_project_id')

    target_project = Project.objects.get(id=target_project_id)
    folder_list = Folder.objects.filter(base_dir_id=target_project.root_dir_id)
    for folder in folder_list:
        delete_folder_sonList(folder.id)
        target_folder = Folder.objects.get(id=folder.id)
        target_folder.delete()

    doc_list = Doc.objects.filter(base_dir_id=target_project.root_dir_id)
    for doc in doc_list:
        target_doc = Doc.objects.get(id=doc.id)
        target_doc.delete()

    target_project.delete()

    return JsonResponse({'code': 0, 'msg': "删除成功"})

@check_request_method('POST')
def rename_doc(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_doc_id = data.get('target_doc_id')
    new_doc_title = data.get('new_doc_title')

    target_doc = Doc.objects.get(id=target_doc_id)
    target_doc.doc_title = new_doc_title
    target_doc.save()

    return JsonResponse({'code': 0, 'msg': "重命名成功"})

@check_request_method('POST')
def rename_folder(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_folder_id = data.get('target_folder_id')
    new_folder_title = data.get('new_folder_title')

    target_folder = Folder.objects.get(id=target_folder_id)
    target_folder.folder_title = new_folder_title
    target_folder.save()

    return JsonResponse({'code': 0, 'msg': "重命名成功"})

@check_request_method('POST')
def rename_project(request):
    if request.method != 'POST':
        return JsonResponse({'code': 1001, 'msg': "请求错误"})

    data = json.loads(request.body.decode("utf-8"))
    target_project_id = data.get('target_project_id')
    new_project_title = data.get('new_project_title')

    target_project = Project.objects.get(id=target_project_id)
    target_project.project_title = new_project_title
    target_project.save()

    return JsonResponse({'code': 0, 'msg': "重命名成功"})