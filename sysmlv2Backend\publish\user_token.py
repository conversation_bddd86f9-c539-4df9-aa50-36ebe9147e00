from datetime import datetime, timedelta
from django.conf import settings
import jwt

def create_token(user_account):
    payload = {
        'user_account': f'{user_account}',
        'exp': datetime.utcnow() + timedelta(days=1)
    }
    return jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')


def parse_token(token):
    # 需要在调用时处理过期异常
    return jwt.decode(token, settings.SECRET_KEY, algorithms='HS256')


def get_user_account(request):
    auth = request.META.get('HTTP_AUTHORIZATION')
    token = auth.split(' ')[1]
    payload = parse_token(token)
    user_account = payload['user_account']
    return user_account
