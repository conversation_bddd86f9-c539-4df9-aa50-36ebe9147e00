import { createApp } from 'vue'
import App from './App.vue'
import router from "./router/index.js";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import axios from "axios"
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';

const app = createApp(App);

app.use(ElementPlus)
app.use(router)
app.use(Antd).mount('#app');

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}


app.config.globalProperties.$axios = axios;





