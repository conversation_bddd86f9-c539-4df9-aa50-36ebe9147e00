<template>
  <div class="doc-card-container" @click="jumpToDoc" :class="{ 'doc-card-container-selected': isSelected }">
    <div class="doc-card-content" >
      <el-row>
        <el-col :span="2"><div /></el-col>

        <el-col :span="4" class="doc-card-icon">
          <el-icon :class="{ 'selected': isSelected }"><Document /></el-icon>
        </el-col>

        <el-col :span="15">
          <a class="doc-card-title" :class="{ 'selected': isSelected }">{{ docObj.doc_title }}</a>
        </el-col>

        <el-col :span="3" class="doc-card-icon">
          <el-dropdown>
            <button class="more-button">
              <el-icon><MoreFilled /></el-icon>
            </button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="copyPath">分享</el-dropdown-item>
                <el-dropdown-item @click="renameVisible = true">重命名</el-dropdown-item>
                <el-dropdown-item>
                  <el-popconfirm
                      confirm-button-text="Yes"
                      cancel-button-text="No"
                      :icon="InfoFilled"
                      icon-color="#626AEF"
                      title="您确定删除该图吗?"
                      @confirm="confirmEvent"
                      @cancel="cancelEvent"
                  >
                    <template #reference>删除</template>
                  </el-popconfirm>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
      </el-row>
    </div>
  </div>

  <el-dialog
      v-model="renameVisible"
      title="重命名图"
      style="max-width: 400px"
  >
    <el-form :model="newDocForm">
      <el-form-item label="图标题" :label-width="formLabelWidth">
        <el-input v-model="newDocForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="renameVisible = false">取消</el-button>
        <el-button type="primary" @click="renameDoc(newDocForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from "axios";
import {defineProps, onBeforeMount} from 'vue';
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import useClipboard from "vue-clipboard3";
import { nextTick, ref, onMounted } from "vue";

const { toClipboard } = useClipboard();
const renameVisible = ref(false);
const newDocForm = ref({
  title: "",
});
const formLabelWidth = "90px";

// const isSelected = ref(true);

const props = defineProps({
  docObj: {
    type: Object,
    required: true,
  },
  reGetDocs: {
    type: Function,
    required: true
  },
  openDoc: {
    type: Function,
    required: true
  },
  isSelected: Boolean
});

const showSelectedDoc = async () => {
  await nextTick();
  // const temp = sessionStorage.getItem("selectedDoc");
  console.log("选中文档：");
  // if (temp !== null) {
  //   console.log(document.getElementById(props.docObj.document_id));
  //   document.getElementById(temp).style.background = "#d7d7f6";
  //   // byClass.background = '#7575e1';
  // }
};

const copyPath = async () => {
  const str = "target url";
  try {
    await toClipboard(str);
    ElMessage({
      showClose: true,
      message: "已复制文档地址至剪贴板",
      type: "success",
    });
  } catch (e) {
    console.log(e);
  }
};

const jumpToDoc = () => {
  // alert("跳转文档");
  // props.jumpToDoc(props.docObj);
  props.openDoc(props.docObj.id);
  window.localStorage.setItem("select", props.docObj.id);
  props.reGetDocs();
};

const renameDoc = (newTitle) => {
  renameVisible.value = !renameVisible.value;

  let data = JSON.stringify({
    target_doc_id: props.docObj.id,
    new_doc_title: newTitle,
  });

  var config = {
    method: 'post',
    url: '/api/renameDoc',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "重命名成功",
            type: "success",
          });
        }

        props.reGetDocs();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const deleteDoc = () => {
  //删除文档
  let data = JSON.stringify({
    target_doc_id: props.docObj.id,
  });

  let config = {
    method: 'post',
    url: '/api/deleteDoc',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
        }

        props.reGetDocs();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const checkSelected = () => {
  const selectedDocID = window.localStorage.getItem("select");
  if (selectedDocID === props.docObj.id) {
    isSelected.value = true;
  }
}

const confirmEvent = () => {
  console.log("confirm!");
  deleteDoc();
};
const cancelEvent = () => {
  console.log("cancel!");
};

onMounted(() => {

  checkSelected();
});

onBeforeMount(() => {

})
</script>

<style scoped>
.doc-card-container {
  width: 100%;
  height: 32px;
  margin-top: 5px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.doc-card-container:hover {
  background-color: #e2e2e8;
}

.doc-card-content {
  line-height: 32px;
}

.doc-card-container-selected {
  background: rgba(0, 191, 255, 0.11);
}

.doc-card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.doc-card-title {
  float: left;
  font-size: 17px;
  text-align: left;
}

.selected {
  color:rgba(30,144,255,0.91)
}

.more-button {
  background-color: Transparent;
  border-style: none;
  outline: none;
  cursor: pointer;
}
.more-button:hover {
  background-color: #d1d1d7;
}
</style>