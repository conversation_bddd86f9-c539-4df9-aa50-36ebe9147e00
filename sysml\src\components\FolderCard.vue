<template>
  <div class="doc-card-container">
    <div class="doc-card-content">
      <el-row>
        <el-col :span="2" @click="switchShow"><div /></el-col>

        <el-col :span="4" class="doc-card-icon" @click="switchShow">
          <el-icon v-if="showSons"><FolderOpened /></el-icon>
          <el-icon v-else><Folder /></el-icon>
        </el-col>

        <el-col :span="12" @click="switchShow">
          <a class="doc-card-title">{{ folderObj.folder_title }}</a>
        </el-col>

        <el-col :span="3" class="doc-card-icon-act">
          <el-dropdown>
            <el-button class="more-button" @click="dialogFormVisible = true">
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="新建文档"
                  placement="top-start"
              >
                <el-icon><DocumentAdd /></el-icon>
              </el-tooltip>
            </el-button>
          </el-dropdown>
        </el-col>
        <el-col :span="3" class="doc-card-icon-act">
          <el-dropdown>
            <el-button class="more-button">
              <el-icon><MoreFilled /></el-icon>
            </el-button>

            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="renameVisible = true"
                >重命名</el-dropdown-item
                >
                <!--                <el-dropdown-item @click="copyPath">分享</el-dropdown-item>-->
                <el-dropdown-item>
                  <el-popconfirm
                      confirm-button-text="Yes"
                      cancel-button-text="No"
                      :icon="InfoFilled"
                      icon-color="#626AEF"
                      title="删除项目会一同删除其目录下所有子图，您确定删除吗"
                      @confirm="confirmEvent"
                      @cancel="cancelEvent"
                  >
                    <template #reference>删除</template>
                  </el-popconfirm>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
      </el-row>
    </div>
  </div>

  <div v-if="showSons">
    <son-doc-card
        v-for="doc in folderObj.doc_list"
        :key="doc.id"
        :doc-obj="doc"
        :re-get-docs="reGetDocs"
        :jump-to-doc="jumpToSonDoc"
        :open-doc="openDoc"
        :is-selected="selectedDoc.id === doc.id"
    ></son-doc-card>
  </div>

  <el-dialog
      v-model="dialogFormVisible"
      title="新建文档"
      style="max-width: 400px"
  >
    <el-form :model="newFolderForm">
      <el-form-item label="文档标题" :label-width="formLabelWidth">
        <el-input v-model="newFolderForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="createSonDoc(newFolderForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
  <el-dialog
      v-model="renameVisible"
      title="重命名文件夹"
      style="max-width: 400px"
  >
    <el-form :model="newDocForm">
      <el-form-item label="文件夹标题" :label-width="formLabelWidth">
        <el-input v-model="newDocForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="renameVisible = false">取消</el-button>
        <el-button type="primary" @click="renameFolder(newDocForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from "axios";
import { defineProps } from 'vue';
import SonDocCard from "./SonDocCard.vue";
import { ref, onMounted } from "vue";
import { ElMessage } from "element-plus";

let showSons = ref(false);
const newFolderForm = ref({
  title: "",
});
const dialogFormVisible = ref(false);
const renameVisible = ref(false);
const newDocForm = ref({
  title: "",
});
const formLabelWidth = "90px";

const switchShow = () => {
  // alert("显示子文档");
  showSons.value = !showSons.value;
  console.log("准备存文档: " + showSons.value);
  sessionStorage.setItem(folderKey, showSons.value);
};

const props = defineProps({
  folderObj: {
    type: Object,
    required: true,
  },
  reGetDocs: {
    type: Function,
    required: true
  },
  openDoc: {
    type: Function,
    required: true
  },
  selectedDoc: Object
});

const folderKey = "folder" + props.folderObj.id;

const renameFolder = (newTitle) => {
  renameVisible.value = !renameVisible.value;

  let data = JSON.stringify({
    target_folder_id: props.folderObj.id,
    new_folder_title: newTitle,
  });

  var config = {
    method: 'post',
    url: '/api/renameFolder',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "重命名成功",
            type: "success",
          });
        }

        props.reGetDocs();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const deleteFolder = () => {
  let data = JSON.stringify({
    target_folder_id: props.folderObj.id,
  });

  let config = {
    method: 'post',
    url: '/api/deleteFolder',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "删除成功",
            type: "success",
          });
        }

        props.reGetDocs();
      })
      .catch(function (error) {
        console.log(error);
      });
};

const createSonDoc = (docTitle) => {
  //在文件夹下创建文档
  dialogFormVisible.value = false;

  let data = JSON.stringify({
    title: docTitle,
    base_dir_id: props.folderObj.id
  });

  let config = {
    method: "post",
    url: "/api/createDoc",
    headers: {
      "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "创建成功",
            type: "success",
          });
        }

        props.reGetDocs();
      })
      .catch(function (error) {
        console.log(error);
      });
  // props.reGetDocs();
};

const jumpToSonDoc = (doc) => {
  // alert("请求查看文档：" + doc.document_title + " id: " + doc.document_id);
  alert("请求查看子文档:");

};

onMounted(() => {
  console.log("请求获取文件夹");
  const temp = sessionStorage.getItem(folderKey);
  if (temp !== null) {
    console.log("文件夹状态： " + temp);
    if (temp === "true") {
      showSons.value = true;
    } else {
      showSons.value = false;
    }
  }
});

const confirmEvent = () => {
  console.log("confirm!");
  deleteFolder();
};
const cancelEvent = () => {
  console.log("cancel!");
};
</script>

<style scoped>
.doc-card-container {
  width: 100%;
  height: 32px;
  margin-top: 5px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.doc-card-container:hover {
  background-color: #e2e2e8;
}

.doc-card-content {
  line-height: 32px;
}

.doc-card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.doc-card-icon-act {
  display: flex;
  justify-content: center;
  align-items: center;
}
.doc-card-icon-act:hover {
  background-color: #d1d1d7;
}

.doc-card-title {
  float: left;
  font-size: 18px;
  text-align: left;
}

.more-button {
  background-color: Transparent;
  border-style: none;
  outline: none;
  cursor: pointer;
}
</style>
