<template>
  <div id="main">
    <el-form
        :rules="rules"
        :model="loginForm"
        ref="loginFormRef"
        class="login_container"
        label-position="right"
        label-width="70px"
    >
      <h2 class="login_title">用户登录</h2>

      <el-form-item label="用户名" prop="email">
        <el-input
            id="email"
            type="text"
            v-model="loginForm.username"
            autocomplete="off"
            placeholder="请输入用户名"
            :prefix-icon="UserFilled"
        ></el-input>
      </el-form-item>
      <el-form-item label="密码" prop="pwd">
        <el-input
            id="pwd"
            type="password"
            v-model="loginForm.pwd"
            autocomplete="off"
            placeholder="请输入密码"
            :prefix-icon="Key"
        >
          <template #append>
            <el-button @click="toForget">忘记密码</el-button>
          </template>
        </el-input>
      </el-form-item>

      <div class="login_buttons">
        <el-button @click="toRegister">注册</el-button>
        <el-button
            class="login_buttons_login"
            type="primary"
            @click="submitForm()"
        >登录</el-button
        >
      </div>
    </el-form>
  </div>
</template>

<script setup>
import axios from "axios";
import {Key, UserFilled} from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { ref } from "vue";
import { ElMessage } from "element-plus";

const loginForm = ref({
  username: "",
  pwd: "",
});

let validateUser_ID = (rule, value, callback) => {
  if (registerForm.value.user_name === "") {
    callback(new Error("请输入用户名"));
  } else {
    let len = registerForm.value.user_name.length;
    if (!(len >= 2 && len <= 16)) {
      callback(new Error("用户名长度应为2-16"));
    }
    callback();
  }
};

const validatePwd = (rule, value, callback) => {
  if (loginForm.value.pwd === "") {
    callback(new Error("请输入密码"));
  } else {
    let len = loginForm.value.pwd.length;
    if (!(len >= 6 && len <= 16)) {
      callback(new Error("密码长度应为6-16"));
    }
    callback();
  }
};

const rules = {
  username: [{ validator: validateUser_ID, trigger: "blur" }],
  pwd: [{ validator: validatePwd, trigger: "blur" }],
};

const router = useRouter();

const toRegister = () => {
  router.push({ path: "/register" });
};

const toForget = () => {
  router.push({ path: "/forget" });
};

const loginFormRef = ref();

const submitForm = () => {
  loginFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.stringify({
        username: loginForm.value.username,
        password: loginForm.value.pwd,
      });

      let config = {
        method: "post",
        url: "/api/login",
        headers: {
          'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
          'Content-Type': 'application/json'
        },
        data: data,
      };

      axios(config)
          .then(function (response) {
            console.log(JSON.stringify(response.data));
            if (response.data.code === 0) {
              ElMessage({
                showClose: true,
                message: "登录成功",
                type: "success",
              });

              window.localStorage.setItem("user", JSON.stringify(response.data.data.user_info));
              router.push({ path: "/" });
            } else if (response.data.code === 2002) {
              ElMessage({
                showClose: true,
                message: response.data.msg,
                type: "error",
              });
            } else if (response.data.code === 2003) {
              ElMessage({
                showClose: true,
                message: response.data.msg,
                type: "error",
              });
            }
          })
          .catch(function (error) {
            console.log(error);
          });
    }
  });
};
</script>

<style scoped>
#main {
  background-position: center;
  background-size: cover;
  background-image: url("../../assets/img/Login/background.svg");
  height: 100%;
  width: 100%;

  position: fixed;
  margin: 0;
  padding: 0;
}

.login_container {
  border-radius: 15px;
  background-clip: padding-box;
  margin: 160px auto;
  width: 450px;
  padding: 35px 35px 15px 35px;
  background: #fff;
  border: 1px solid #eaeaea;
  box-shadow: 0 0 25px #cac6c6;
  justify-content: center;
  align-items: center;
}

.login_title {
  margin-bottom: 20px;
  text-align: center;
  color: #505458;
}

.login_buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
  /*margin: 0px;*/
}
</style>
