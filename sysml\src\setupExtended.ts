import { MonacoEditorLanguageClientWrapper, UserConfig } from 'monaco-editor-wrapper';
import { configureWorker, defineUserServices } from './setupCommon.js';

let wrapper : any;

export const setupConfigExtended = (codeContent): UserConfig => {
    const extensionFilesOrContents = new Map();
    extensionFilesOrContents.set('/language-configuration.json', new URL('../language-configuration.json', import.meta.url));
    extensionFilesOrContents.set('/sysml-grammar.json', new URL('../syntaxes/sysml.tmLanguage.json', import.meta.url));

    return {
        wrapperConfig: {
            serviceConfig: defineUserServices(),
            editorAppConfig: {
                $type: 'extended',
                languageId: 'sysml',
                code: codeContent,
                useDiffEditor: false,
                extensions: [{
                    config: {
                        name: 'sysml-web',
                        publisher: 'generator-langium',
                        version: '1.0.0',
                        engines: {
                            vscode: '*'
                        },
                        contributes: {
                            languages: [{
                                id: 'sysml',
                                extensions: [
                                    '.sysml'
                                ],
                                configuration: './language-configuration.json'
                            }],
                            grammars: [{
                                language: 'sysml',
                                scopeName: 'source.sysml',
                                path: './sysml-grammar.json'
                            }]
                        }
                    },
                    filesOrContents: extensionFilesOrContents,
                }],                
                userConfiguration: {
                    json: JSON.stringify({
                        'workbench.colorTheme': 'Default Light Modern',
                        'editor.semanticHighlighting.enabled': true
                    })
                }
            }
        },
        languageClientConfig: configureWorker()
    };
};

export const executeExtended = async (htmlElement: HTMLElement, codeContent) => {
    const userConfig = setupConfigExtended(codeContent);
    wrapper = new MonacoEditorLanguageClientWrapper();
    console.log('mark1');
    await wrapper.initAndStart(userConfig, htmlElement);
    console.log('mark2');
};

export const getCode = () => {
    return wrapper.getModel()?.getValue();
}