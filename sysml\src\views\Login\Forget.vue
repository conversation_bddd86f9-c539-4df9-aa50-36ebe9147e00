<template>
  <div id="poster">
    <el-form
        :model="forgetForm"
        ref="forgetFormRef"
        status-icon
        :rules="rules"
        class="register_container"
        label-position="left"
        label-width="80px"
    >
      <h2 class="register_title">忘记密码</h2>

      <el-form-item label="邮箱" prop="email" class="form-item">
        <el-input
            type="text"
            v-model="forgetForm.username"
            autocomplete="off"
            placeholder="请输入邮箱地址"
            :prefix-icon="Message"
        >
          <template #append>
            <el-button @click="getVerificationCode">发送验证码</el-button>
          </template>
        </el-input>
      </el-form-item>

      <el-form-item label="验证码" prop="verificationCode">
        <el-input
            type="text"
            v-model="forgetForm.verificationCode"
            autocomplete="off"
            placeholder="请输入验证码"
            :prefix-icon="Checked"
        ></el-input>
      </el-form-item>
      <el-form-item label="重置密码" prop="pwd">
        <el-input
            type="password"
            v-model="forgetForm.pwd"
            autocomplete="off"
            placeholder="请输入密码"
            :prefix-icon="Key"
        ></el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="checkPwd" style="width: 100%">
        <el-input
            type="password"
            v-model="forgetForm.checkPwd"
            autocomplete="off"
            placeholder="请再次输入密码"
            :prefix-icon="Key"
        ></el-input>
      </el-form-item>

      <div class="register_buttons">
        <el-button @click="toLogin_User">去登录</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')"
        >确认重置</el-button
        >
      </div>
    </el-form>
  </div>
</template>

<script setup>
import axios from "axios";
import { Key, Message, Checked } from "@element-plus/icons-vue";
import { useRouter } from "vue-router";
import { ref } from "vue";
import { ElMessage } from "element-plus";

const forgetForm = ref({
  username: "",
  pwd: "",
  checkPwd: "",
  verificationCode: "",
});

let validateEmail = (rule, value, callback) => {
  if (forgetForm.value.username === "") {
    callback(new Error("请输入邮箱地址"));
  } else {
    let patt = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    if (!patt.test(forgetForm.value.username)) {
      callback(new Error("邮箱格式不合法"));
    }

    callback();
  }
};
let validatePwd = (rule, value, callback) => {
  if (forgetForm.value.pwd === "") {
    callback(new Error("请输入密码"));
  } else {
    let len = forgetForm.value.pwd.length;
    if (!(len >= 6 && len <= 16)) {
      callback(new Error("密码长度应为6-16"));
    }
    // if (this.ruleForm.checkPwd.length !== '') {
    //   this.$refs.ruleForm.validateField('checkPwd');
    // }
    callback();
  }
};
let validatePwd2 = (rule, value, callback) => {
  if (forgetForm.value.checkPwd === "") {
    callback(new Error("请再次输入密码"));
  } else if (forgetForm.value.checkPwd !== forgetForm.value.pwd) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};
let validateCode = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请输入验证码"));
  } else {
    callback();
  }
};

let rules = {
  username: [{ validator: validateEmail, trigger: "blur" }],
  pwd: [{ validator: validatePwd, trigger: "blur" }],
  checkPwd: [{ validator: validatePwd2, trigger: "blur" }],
  verificationCode: [{ validator: validateCode, trigger: "blur" }],
};

const router = useRouter();

const forgetFormRef = ref();

const submitForm = () => {
  forgetFormRef.value.validate((valid) => {
    if (valid) {
      const data = JSON.stringify({
        user_email: forgetForm.value.username,
        new_password: forgetForm.value.pwd,
        reset_code: forgetForm.value.verificationCode,
      });

      const config = {
        method: "post",
        url: "password/reset/",
        headers: {
          "User-Agent": "Apifox/1.0.0 (https://apifox.com)",
          "Content-Type": "application/json",
        },
        data: data,
      };

      axios(config)
          .then(function (response) {
            console.log(JSON.stringify(response.data));
          })
          .catch(function (error) {
            console.log(error);
          });
    } else {
      console.log("error submit!!");
      return false;
    }
  });
};

const getVerificationCode = () => {
  if (forgetForm.value.username === "") {
    ElMessage({
      showClose: true,
      message: "请输入邮箱地址",
      type: "warning",
    });
    return;
  } else {
    const patt = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    if (!patt.test(forgetForm.value.username)) {
      ElMessage({
        showClose: true,
        message: "邮箱格式不合法",
        type: "warning",
      });
      return;
    }
  }

  const data = JSON.stringify({
    user_email: forgetForm.value.username,
  });

  const config = {
    method: "post",
    url: "/user/forget/",
    headers: {
      "User-Agent": "Apifox/1.0.0 (https://www.apifox.cn)",
      "Content-Type": "application/json",
    },
    data: data,
  };

  axios(config)
      .then((response) => {
        console.log(JSON.stringify(response.data));
        if (response.data.code === 0) {
          this.$message({
            message: response.data.msg,
            type: "success",
          });
        } else {
          this.$message({
            message: response.data.msg,
            type: "error",
          });
        }
      })
      .catch((error) => {
        console.log(error);
        alert(error);
      });
};

const toLogin_User = () => {
  router.push({ path: "/login" });
};
</script>

<style scoped>
#poster {
  background-position: center;
  background-size: cover;
  background-image: url("../../assets/img/Login/background.svg");
  height: 100%;
  width: 100%;
  position: fixed;
  margin: 0px;
  padding: 0px;
}
.register_container {
  border-radius: 15px;
  background-clip: padding-box;
  margin: 90px auto;
  width: 600px;
  padding: 35px 35px 15px 35px;
  background: #fff;
  border: 1px solid #eaeaea;
  box-shadow: 0 0 25px #cac6c6;
}
.register_title {
  margin-left: auto;
  text-align: center;
  /*margin-right: auto;*/
  margin-bottom: 20px;
  color: #505458;
}
.register_buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  /*margin: 0px;*/
}

.form-item {
  width: 100%;
}
</style>
