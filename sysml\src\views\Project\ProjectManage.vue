<template>
  <div class="common-layout">
    <el-container>
      <el-aside width="200px" class="page-aside">
        <div class="aside-card aside-selected">
          <el-icon :size="20" class="aside-icon aside-icon-selected"><Reading /></el-icon>
          <span style="color:rgba(30,144,255,0.91)">项目空间</span>
        </div>
        <div class="aside-card">
          <el-icon :size="20" class="aside-icon"><Clock /></el-icon>
          <span>浏览历史</span>
        </div>
        <div class="aside-card">
          <el-icon :size="20" class="aside-icon"><Service /></el-icon>
          <span>关于我们</span>
        </div>
      </el-aside>
      <el-container>
        <el-header class="header-container">
          <el-row style="width: 100%">
            <el-col :span="4">
              <div class="header-element-container">
                <img src="../../assets/img/logo.png" class="logo" alt="logo">
              </div>
            </el-col>
            <el-col :span="16"></el-col>
            <el-col :span="4">
              <div class="header-element-container">
                <el-button v-if="baseInfo.isLogin" @click="logout" round>Logout</el-button>
                <el-button v-else @click="toLogin_User" round>Login</el-button>
              </div>

            </el-col>
          </el-row>
        </el-header>

        <el-main class="main-container">
          <el-row>
            <el-col :span="20">
              <div class="buttons-container">
                <el-button
                    type="primary"
                    :icon="Plus"
                    class="create-icon"
                    @click="newProjectVisible = true"
                >新建项目</el-button>
              </div>

              <div class="divide-line"></div>

              <div class="projects-list">
                <project-card
                    v-for="project in baseInfo.projectList"
                    :project-obj="project"
                    :into-project="intoProject"
                    :re-get-projects="getProjects"
                >
                </project-card>
              </div>
            </el-col>
            <el-col :span="4"></el-col>
          </el-row>

        </el-main>
      </el-container>
    </el-container>
  </div>

  <el-dialog v-model="newProjectVisible" title="新建项目" style="max-width: 400px">
    <el-form :model="newProjectForm">
      <el-form-item label="项目标题" :label-width="formLabelWidth">
        <el-input v-model="newProjectForm.title" autocomplete="off" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="newProjectVisible = false">取消</el-button>
        <el-button type="primary" @click="createProject(newProjectForm.title)">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import ProjectCard from "../../components/ProjectCard.vue";
import {onBeforeMount, onMounted, ref} from "vue";
import {Clock, Plus, Service} from "@element-plus/icons-vue";
import {useRouter} from "vue-router";
import axios from "axios";
import {ElMessage} from "element-plus";

const router = useRouter();

const baseInfo = ref({
  isLogin: false,
  currentUser: {},
  projectList: []
})

const newProjectForm = ref({
  title: "",
});
const newProjectVisible = ref(false);
const formLabelWidth = "90px";

onMounted(() => {
  // testInit();
  getProjects();
});

onBeforeMount(() => {
  initUser();
})

const initUser = () => {
  let user = JSON.parse(window.localStorage.getItem("user"));
  if (user === null) {
    baseInfo.value.isLogin = false;
  } else {
    baseInfo.value.isLogin = true;
    baseInfo.value.currentUser = user;
  }
  // baseInfo.value.root_dir = baseInfo.value.currentUser.root_dir_id;

}

const getProjects = () => {
  if (baseInfo.value.isLogin === false) {
    return;
  }

  let data = JSON.stringify({
    // base_dir_id: baseInfo.value.root_dir
    target_user_id: baseInfo.value.currentUser.id
  });

  let config = {
    method: 'post',
    url: '/api/getProjects',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        baseInfo.value.projectList = response.data.data;
      })
      .catch(function (error) {
        console.log(error);
      });
}

const createProject = (title) => {
  if (title.length === 0) {
    ElMessage({
      showClose: true,
      message: "文档名称不可为空",
      type: "error",
    });
    return;
  }

  newProjectVisible.value = !newProjectVisible.value;

  let data = JSON.stringify({
    title: title,
    base_user_id: baseInfo.value.currentUser.id
  });

  let config = {
    method: 'post',
    url: '/api/createProject',
    headers: {
      'User-Agent': 'Apifox/1.0.0 (https://www.apifox.cn)',
      'Content-Type': 'application/json'
    },
    data : data
  };

  axios(config)
      .then(function (response) {
        console.log(JSON.stringify(response.data));

        if (response.data.code === 0) {
          ElMessage({
            showClose: true,
            message: "创建成功",
            type: "success",
          });
        }

        getProjects()
      })
      .catch(function (error) {
        console.log(error);
      });
}

const toLogin_User = () => {
  router.push({ path: "/login" });
};

const intoProject = (project) => {
  // console.log("请求查看项目: " + project.id);
  const targetPath = "/project/" + project.id
  router.push({path: targetPath})
}
</script>

<style scoped>
.divide-line{
  border-top: 1px solid #ddd;
  margin-top: 5px;
  margin-bottom: 5px;
}

.page-title {
  font-size: 28px;
}

.header-container {
  height: 40px;
  display:flex;
  //justify-content: center;
  align-items:center;
}

.main-container {
  padding: 5px;
}

.create-icon {
  float: right;
  margin-right: 20px;
}

.buttons-container {
  height: 32px;
}

.page-aside {
  height: 100vh;
  background: rgba(88,88,88,0.08);
}

.aside-card {
  height: 48px;
  width: 100%;
  cursor: pointer;
  text-align: center;
  line-height: 48px;

  font-size: 18px;
  display:flex;
  justify-content: center;
  align-items:center;
}
.aside-card:hover {
  background-color: #e2e2e8;
}

.aside-icon {
  margin-right: 4px;
}

.aside-icon-selected {
  color:rgba(30,144,255,0.91)
}

.aside-selected {
  background: rgba(0, 191, 255, 0.11);

}

.header-element-container {
  height: 50px;
  border: none;
  display:flex;
  //justify-content: center;
  align-items:center;
}

.logo {
  height: 50px;
}
</style>