{"name": "sysml", "description": "Please enter a brief description here", "version": "0.0.1", "files": ["bin", "out", "src"], "type": "module", "scripts": {"build": "tsc -b tsconfig.src.json && node esbuild.mjs", "watch": "concurrently -n tsc,esbuild -c blue,yellow \"tsc -b tsconfig.src.json --watch\" \"node esbuild.mjs --watch\"", "lint": "eslint src --ext ts", "langium:generate": "langium generate", "langium:generate:production": "langium generate --mode=production", "langium:watch": "langium generate --watch", "vscode:prepublish": "npm run build && npm run lint", "build:web": "npm run build", "bundle": "vite build", "bundle:serve": "http-server ./dist --port 5175", "dev": "vite", "dev:debug": "vite --debug --force", "serve": "npm run dev", "test": "vitest run"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@codingame/monaco-vscode-editor-service-override": "~3.2.3", "@codingame/monaco-vscode-keybindings-service-override": "~3.2.3", "@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.3.1", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "chalk": "~5.3.0", "commander": "~11.0.0", "element-plus": "^2.9.9", "langium": "~3.4.0", "monaco-editor": "npm:@codingame/monaco-vscode-editor-api@~3.2.3", "monaco-editor-wrapper": "~4.0.2", "monaco-languageclient": "~8.1.1", "vite-plugin-monaco-editor": "^1.1.0", "vscode": "npm:@codingame/monaco-vscode-api@~3.2.3", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-router": "^4.5.1"}, "devDependencies": {"@codingame/esbuild-import-meta-url-plugin": "~1.0.2", "@types/node": "^18.0.0", "@types/vscode": "~1.67.0", "@typescript-eslint/eslint-plugin": "~7.3.1", "@typescript-eslint/parser": "~7.3.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "concurrently": "~8.2.1", "esbuild": "~0.20.2", "eslint": "~8.57.0", "http-server": "~14.1.1", "langium-cli": "~3.4.0", "typescript": "~5.1.6", "vite": "6.2.6", "vitest": "3.0.5", "vue-tsc": "^2.2.8"}, "volta": {"node": "18.19.1", "npm": "10.2.4"}, "displayName": "sysml", "engines": {"vscode": "^1.67.0", "node": ">=18.0.0"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "sysml", "aliases": ["Sysml", "sysml"], "extensions": [".sysml"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "sysml", "scopeName": "source.sysml", "path": "syntaxes/sysml.tmLanguage.json"}]}, "activationEvents": ["onLanguage:sysml"], "main": "./out/extension/main.cjs", "bin": {"sysml-cli": "./bin/cli.js"}}